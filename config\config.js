// app配置相关js文件，开发配置文件：生产环境和开发环境隔离
import { isDev } from '@/common/EnvUtils.js'
// 定义登录页默认标题和背景图
const LOGIN_TITLE = '智慧园区平台'
const LOGIN_BGIMG =
  'http://172.16.11.168:30174/zfc/2023/09/19/d7f518f2-d1a9-441d-a8a2-09d91dcc8022.png'
const defalutOutLink = 'https://www.baidu.com' //默认的外部链接跳转地址
const unified = false //是否采用统一版本： 统一版本为true；(false:分端导出)
const MOCK = false
const storeSuffix = 'door'
const version = '20211231-21:10'
const versionNum = 2
const maxVideoSize = 300 * 1024 * 1024
const maxVideoMsg = '视频文件大小超过300MB，请重新上传！'
const colorIndex = uni.getStorageSync('colorIndex') || 0 // 切换主题索引
const colorList = ['#007AFF', '#FF0000', '#00FF00'] // 切换主题颜色数组
const clientId = 'bVS46ElU' //请求头参数
const appCode = 'ydmh'
const tenantIdGlobal = 'system' //全局变量租户id
// const devURL = 'http://172.16.11.82:18080'	//开发环境 
const devURL = 'http://172.16.11.82:18080'
// const buildURL = 'https://sdltzhxz.jsbnb.top'   //生产环境
const buildURL = 'http://172.16.11.82:18080'
const appLabel = '智慧园区'
const corpId = 'ding14856810d9460a19'
 // const corpId = 'dinge283b8915bcc00444ac5d6980864d335'
const apiUrl = ''
// const apiHeader = '/zhyq_app'
const apiHeader = ''
const apiHeader2 = ''
const baseUrl = isDev() ? devURL : buildURL
const qqMapWXKey = 'UN7BZ-UEOKU-O7BVN-GORR2-ARFZ3-STFX6' //腾讯地图key
const aMapKey = '6a3c7c68739e5969d17ec976a7e5dedd' //高德地图key,
const IMAGE_URL = 'http://1************:32489/portal/unidoor_hm'
const IMG_URL = `${apiHeader}/cms/v1/files/downloadBinary/`
const IMG_URL2 = `${apiHeader}/park${apiUrl}/majorParking/parkRecords/downloadBinary/`
const devSocketURL = '' //开发测试环境socket连接地址
const proSocketURL = '' //生产环境socket连接地址
const socketURL = isDev() ? devSocketURL : proSocketURL


//app专属配置
// #ifdef APP-PLUS
const isTextTenantId = false //app- 是否开启手动输入租户 true:开启 false:不开启
const certificateHost = '************' //app- 证书验证的服务器端域
const certificateVersion = '1.0.1' //app-证书版本号
const certificateData =
  'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlEZHpDQ0FsOENGQlR5N3VOOFowV05vZ3VZSStkTG4zWmdBZUJFTUEwR0NTcUdTSWIzRFFFQkN3VUFNSGd4DQpDekFKQmdOVkJBWVRBa05PTVFz\r\nd0NRWURWUVFJREFKTVlqRUxNQWtHQTFVRUJ3d0NhbTR4RERBS0JnTlZCQW9NDQpBMlp4YURFUU1BNEdBMVVFQ3d3SGMyVmpkR2x2YmpFTU1Bb0dBMVVFQXd3RGVuZHNNU0V3SHdZSktvWklodmNODQpBUWtCRmhKNmFHRnVaM2RzTXpBeVFERTJNeTVqYjIwd0hoY05Nak13TlRNeE1ESXhPREkxV2hjTk16TXdOVEk0DQpNREl4T0RJMVdqQjRNUXN3Q1FZRFZRUUdFd0pEVGpFTE1Ba0dBMVVFQ0F3Q1RHSXhDekFKQmdOVkJBY01BbXB1DQpNUXd3Q2dZRFZRUUtEQU5tY1dneEVEQU9CZ05WQkFzTUIzTmxZM1JwYjI0eEREQUtCZ05WQkFNTUEzcDNiREVoDQpNQjhHQ1NxR1NJYjNEUUVKQVJZU2VtaGhibWQzYkRNd01rQXhOak11WTI5dE1JSUJJakFOQmdrcWhraUc5dzBCDQpBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3akVvVWtST0txQUtyK2VtdXZIOUFRNDZmSldDVVliMFFXeTZxQ0R3DQo1eXJpQndGSmhGa21FZlVIc1gvQTEyK0Z2c0tjTlNPNk1Bc0tnS3JxMGR0NHcxNDJ5b0VVQnR2T00wL0RvVTZhDQp1SWxUVWNzRHpwYTdjWGtUeFE1enJEbmRHdE0yVGVTRS9wampXT3lyMi9JS0xmUXAwcHRFL0dadUFEdHkzS1BYDQovbGg0elEzNENtSDlRcVJJcVVlbkx6RVBnL3ZGNFFyd2E5dXlHMU9TTm1BbXNwNHorRnBESzFFUG5OeWxZY04wDQo0RzN0eVdNQTNrUGdDT2orcUJWcTVJd2k0NFBaRnIvbldDQ2N2cjhXUjNMb3ZQYTlwK3BwVTRVMmxhekVhem5NDQpkT0cybldJQTdQdFZTZFk5SmpRdU9pOUwvaEI0bXNFWFpib291cHhMU0cyejdRSURBUUFCTUEwR0NTcUdTSWIzDQpEUUVCQ3dVQUE0SUJBUUEvaUxjalpMQjZvcU1RMUo4UC8wQzJMSzVxNXpFYVRVTjh3RlpnS25FM3pFSGFvY3N2DQpJdldLaFNDNU04SUxRNWNETHEyb1RZclcxZWE2N0MxTFI4Mmsza0pmNnMxYkRQenpNVnEwVVRkemFKMXpUQkVjDQpEampLNlo5YXNtUUtPWCtUY0QvTmU2dFNWN2dZcGdySG5MKytiMFdKOTNmcHJUV2pISllJanpidWZKYkNRZ1NVDQpHcUcralRrc0t6ZzhzOVhsVk0zeEtvM0p1NzgreGhZd3cvYnlLd1BUZEdtb0kxcGl6UDZTOW1OQnFwRGkwZEIzDQp2eWtIbnZtd1dmR3hRTVNHY013VzlGS1FGZS9icjNNUEs5VEhPVlJ5cGxoNzM4ekRmaUxpQlByclNoSEt0bWE1DQpDVm9GN2Vhekg0UU8raHdsay9Sajh5a3NYRUtpWTc3Umc1YnUNCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0NCg==' //app-证书文件
const softName = 'yidongmenhu' //APP更新时参数字段
// ios端app在线升级打开app store下载,这里更换成自己项目上线的app地址
const iosUpdateAddress = 'https://apps.apple.com/cn/app/xxxx/xxxx'
// #endif

//分享相关:页面分享配置
const share = {
  title: '移动端统一门户平台',
  // #ifdef MP-WEIXIN
  path: '/pages/index/index', //微信小程序分享路径
  // #endif
  // #ifdef H5 || APP-PLUS
  //微信公众号||APP分享
  desc: '移动端统一门户平台', // 分享描述
  link: '', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
  imgUrl: '', // 分享图标
  // #endif
}

export var config

if (unified) {

  //统一版本配置
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum, //版本号
    maxVideoSize: maxVideoSize, //视频文件限制大小
    maxVideoMsg: maxVideoMsg, //视频文件超限提示
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    isText_tenantId: isTextTenantId, // 是否开启手动输入租户 true:开启 false:不开启
    certificateHost: certificateHost, // 证书验证的服务器端域
    certificate_version: certificateVersion, //证书版本号
    certificate_data: certificateData,
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    share: share,
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
} else {
  //分端处理
  // #ifdef APP-PLUS
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    isText_tenantId: isTextTenantId, // 是否开启手动输入租户 true:开启 false:不开启
    certificateHost: certificateHost, // 证书验证的服务器端域
    certificate_version: certificateVersion, //证书版本号
    certificate_data: certificateData,
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    share: share,
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    softName,
	iosUpdateAddress,
  apiUrl:apiUrl,
  apiHeader:apiHeader,
  apiHeader2:apiHeader2,
  IMG_URL,
   IMG_URL2,
  appLabel,
  corpId
  }
  // #endif
  // #ifdef MP-WEIXIN
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    share: share,
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    MP_TENANTID: 'system', //微信小程序授权登录获取手机号接口默认tenantId
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
  //微信公众服务号
  // #ifdef H5-WEIXIN
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    share: share,
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
  // #ifdef H5
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    isHideNavBar:true, //钉钉H5中是否隐藏原生导航栏
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
  //微信公众订阅号
  // #ifdef H5-WEIXIN-SUB
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    share: share,
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
  // #ifdef MP-DINGTALK
  
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    SOCKETURL: socketURL,
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    apiUrl:apiUrl,
    isHideNavBar:true, //钉钉H5中是否隐藏原生导航栏
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
  // #ifdef H5-DINGTALK
  config = {
    mock: MOCK,
    storeSuffix: storeSuffix,
    version: version,
    versionNum: versionNum,
    maxVideoSize: maxVideoSize,
    maxVideoMsg: maxVideoMsg,
    clientId: clientId, //请求头参数
    appCode: appCode, //文件上传参数定义
    tenantId_global: tenantIdGlobal, //全局变量租户id
    BASEURL: baseUrl, //后台url
    QQMapWXKey: qqMapWXKey, //腾讯地图key
    AMapKey: aMapKey, //高德地图key
    colorIndex: colorIndex, // 主题配置索引
    colorList: colorList, // 主题颜色数组
    IMAGE_URL: IMAGE_URL, // 图片基础地址
    SOCKETURL: socketURL,
	  applicationId: 'tymh',
    LOGIN_TITLE,
    LOGIN_BGIMG,
    defalutOutLink,
    isHideNavBar:true, //钉钉H5中是否隐藏原生导航栏
    apiUrl:apiUrl,
    apiHeader:apiHeader,
    apiHeader2:apiHeader2,
    IMG_URL,
     IMG_URL2,
    appLabel,
    corpId
  }
  // #endif
}
