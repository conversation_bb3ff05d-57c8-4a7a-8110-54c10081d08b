!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){if(!n||0===n.length)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map((t=>{if((t=function(e){return"/"+e}(t))in e)return;e[t]=!0;const n=t.endsWith(".css"),r=n?'[rel="stylesheet"]':"";if(!!o)for(let e=i.length-1;e>=0;e--){const o=i[e];if(o.href===t&&(!n||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${r}`))return;const a=document.createElement("link");return a.rel=n?"stylesheet":"modulepreload",n||(a.as="script",a.crossOrigin=""),a.href=t,document.head.appendChild(a),n?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t()))};function n(e,t){const n=Object.create(null),o=e.split(",");for(let i=0;i<o.length;i++)n[o[i]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const o={},i=[],r=()=>{},a=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),p=Array.isArray,h=e=>"[object Map]"===A(e),g=e=>"[object Set]"===A(e),m=e=>"[object Date]"===A(e),v=e=>"function"==typeof e,y=e=>"string"==typeof e,b=e=>"symbol"==typeof e,w=e=>null!==e&&"object"==typeof e,x=e=>(w(e)||v(e))&&v(e.then)&&v(e.catch),_=Object.prototype.toString,A=e=>_.call(e),S=e=>"[object Object]"===A(e),T=e=>y(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},k=/-(\w)/g,P=E((e=>e.replace(k,((e,t)=>t?t.toUpperCase():"")))),B=/\B([A-Z])/g,M=E((e=>e.replace(B,"-$1").toLowerCase())),I=E((e=>e.charAt(0).toUpperCase()+e.slice(1))),O=E((e=>e?`on${I(e)}`:"")),L=(e,t)=>!Object.is(e,t),D=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},z=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},R=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let N;function j(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],i=y(o)?H(o):j(o);if(i)for(const e in i)t[e]=i[e]}return t}if(y(e)||w(e))return e}const F=/;(?![^(]*\))/g,q=/:([^]+)/,V=/\/\*[^]*?\*\//g;function H(e){const t={};return e.replace(V,"").split(F).forEach((e=>{if(e){const n=e.split(q);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function Q(e){let t="";if(y(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const o=Q(e[n]);o&&(t+=o+" ")}else if(w(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const U=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function W(e){return!!e||""===e}function $(e,t){if(e===t)return!0;let n=m(e),o=m(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=b(e),o=b(t),n||o)return e===t;if(n=p(e),o=p(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=$(e[o],t[o]);return n}(e,t);if(n=w(e),o=w(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),i=t.hasOwnProperty(n);if(o&&!i||!o&&i||!$(e[n],t[n]))return!1}}return String(e)===String(t)}function X(e,t){return e.findIndex((e=>$(e,t)))}const Y=e=>y(e)?e:null==e?"":p(e)||w(e)&&(e.toString===_||!v(e.toString))?JSON.stringify(e,J,2):String(e),J=(e,t)=>t&&t.__v_isRef?J(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[G(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>G(e)))}:b(t)?G(t):!w(t)||p(t)||S(t)?t:String(t),G=(e,t="")=>{var n;return b(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e)),Z=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),ee=["list-item"].map((e=>"uni-"+e));function te(e){if(-1!==ee.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==K.indexOf(t)||-1!==Z.indexOf(t)}const ne=["%","%"],oe=/^([a-z-]+:)?\/\//i,ie=/^data:.*,.*/;function re(e){return e&&(e.appContext?e.proxy:e)}function ae(e){if(!e)return;let t=e.type.name;for(;t&&te(M(t));)t=(e=e.parent).type.name;return e.proxy}function se(e){return 1===e.nodeType}function le(e){if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),j(t)}if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],i=y(o)?H(o):le(o);if(i)for(const e in i)t[e]=i[e]}return t}return j(e)}function ce(e){let t="";if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(p(e))for(let n=0;n<e.length;n++){const o=ce(e[n]);o&&(t+=o+" ")}else t=Q(e);return t.trim()}function ue(e){return 0===e.indexOf("/")}function de(e){return ue(e)?e:"/"+e}function fe(e){return ue(e)?e.slice(1):e}function pe(e,t){for(const n in t)e.style[n]=t[n]}function he(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function ge(e,t){e=e||{},y(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?v(e.success)&&e.success(t):v(e.fail)&&e.fail(t),v(e.complete)&&e.complete(t)}function me(e){return P(e.substring(5))}const ve=he((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[me(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[me(e)],n.call(this,e)}}));function ye(e){return c({},e.dataset,e.__uniDataset)}const be=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function we(e){return{passive:e}}function xe(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:ye(e),offsetTop:n,offsetLeft:o}}function _e(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Ae(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=_e(e[n])}catch(o){t[n]=e[n]}})),t}const Se=/\+/g;function Te(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Se," ");let i=e.indexOf("="),r=_e(i<0?e:e.slice(0,i)),a=i<0?null:_e(e.slice(i+1));if(r in t){let e=t[r];p(e)||(e=t[r]=[e]),e.push(a)}else t[r]=a}return t}function Ce(e,t,{clearTimeout:n,setTimeout:o}){let i;const r=function(){n(i);const r=()=>e.apply(this,arguments);i=o(r,t)};return r.cancel=function(){n(i)},r}class Ee{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const ke=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],Pe=["onLoad","onShow"];const Be=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Me=[];const Ie=he(((e,t)=>{if(v(e._component.onError))return t(e)})),Oe=function(){};Oe.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function i(){o.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,i=n.length;o<i;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],i=[];if(o&&t){for(var r=o.length-1;r>=0;r--)if(o[r].fn===t||o[r].fn._===t){o.splice(r,1);break}i=o}return i.length?n[e]=i:delete n[e],this}};var Le=Oe;const De={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function ze(e,t={},n="light"){const o=t[n],i={};return o?(Object.keys(e).forEach((r=>{let a=e[r];i[r]=(()=>{if(S(a))return ze(a,t,n);if(p(a))return a.map((e=>S(e)?ze(e,t,n):e));if(y(a)&&a.startsWith("@")){const t=a.replace("@","");let n=o[t]||a;switch(r){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in De?De[e]:e}return n}var e;return a})()})),i):e}let Re;class Ne{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Re,!e&&Re&&(this.index=(Re.scopes||(Re.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Re;try{return Re=this,e()}finally{Re=t}}}on(){Re=this}off(){Re=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function je(e){return new Ne(e)}function Fe(){return Re}const qe=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Ve=e=>(e.w&We)>0,He=e=>(e.n&We)>0,Qe=new WeakMap;let Ue=0,We=1;let $e;const Xe=Symbol(""),Ye=Symbol("");class Je{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=Re){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=$e,t=Ke;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=$e,$e=this,Ke=!0,We=1<<++Ue,Ue<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=We})(this):Ge(this),this.fn()}finally{Ue<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const i=t[o];Ve(i)&&!He(i)?i.delete(e):t[n++]=i,i.w&=~We,i.n&=~We}t.length=n}})(this),We=1<<--Ue,$e=this.parent,Ke=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){$e===this?this.deferStop=!0:this.active&&(Ge(this),this.onStop&&this.onStop(),this.active=!1)}}function Ge(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Ke=!0;const Ze=[];function et(){Ze.push(Ke),Ke=!1}function tt(){const e=Ze.pop();Ke=void 0===e||e}function nt(e,t,n){if(Ke&&$e){let t=Qe.get(e);t||Qe.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=qe()),ot(o)}}function ot(e,t){let n=!1;Ue<=30?He(e)||(e.n|=We,n=!Ve(e)):n=!e.has($e),n&&(e.add($e),$e.deps.push(e))}function it(e,t,n,o,i,r){const a=Qe.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&p(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":p(e)?T(n)&&s.push(a.get("length")):(s.push(a.get(Xe)),h(e)&&s.push(a.get(Ye)));break;case"delete":p(e)||(s.push(a.get(Xe)),h(e)&&s.push(a.get(Ye)));break;case"set":h(e)&&s.push(a.get(Xe))}if(1===s.length)s[0]&&rt(s[0]);else{const e=[];for(const t of s)t&&e.push(...t);rt(qe(e))}}function rt(e,t){const n=p(e)?e:[...e];for(const o of n)o.computed&&at(o);for(const o of n)o.computed||at(o)}function at(e,t){(e!==$e||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const st=n("__proto__,__v_isRef,__isVue"),lt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(b)),ct=gt(),ut=gt(!1,!0),dt=gt(!0),ft=pt();function pt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Zt(this);for(let t=0,i=this.length;t<i;t++)nt(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Zt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){et();const n=Zt(this)[t].apply(this,e);return tt(),n}})),e}function ht(e){const t=Zt(this);return nt(t,0,e),t.hasOwnProperty(e)}function gt(e=!1,t=!1){return function(n,o,i){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&i===(e?t?Qt:Ht:t?Vt:qt).get(n))return n;const r=p(n);if(!e){if(r&&f(ft,o))return Reflect.get(ft,o,i);if("hasOwnProperty"===o)return ht}const a=Reflect.get(n,o,i);return(b(o)?lt.has(o):st(o))?a:(e||nt(n,0,o),t?a:an(a)?r&&T(o)?a:a.value:w(a)?e?$t(a):Wt(a):a)}}function mt(e=!1){return function(t,n,o,i){let r=t[n];if(Jt(r)&&an(r)&&!an(o))return!1;if(!e&&(Gt(o)||Jt(o)||(r=Zt(r),o=Zt(o)),!p(t)&&an(r)&&!an(o)))return r.value=o,!0;const a=p(t)&&T(n)?Number(n)<t.length:f(t,n),s=Reflect.set(t,n,o,i);return t===Zt(i)&&(a?L(o,r)&&it(t,"set",n,o):it(t,"add",n,o)),s}}const vt={get:ct,set:mt(),deleteProperty:function(e,t){const n=f(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&it(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return b(t)&&lt.has(t)||nt(e,0,t),n},ownKeys:function(e){return nt(e,0,p(e)?"length":Xe),Reflect.ownKeys(e)}},yt={get:dt,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},bt=c({},vt,{get:ut,set:mt(!0)}),wt=e=>e,xt=e=>Reflect.getPrototypeOf(e);function _t(e,t,n=!1,o=!1){const i=Zt(e=e.__v_raw),r=Zt(t);n||(t!==r&&nt(i,0,t),nt(i,0,r));const{has:a}=xt(i),s=o?wt:n?nn:tn;return a.call(i,t)?s(e.get(t)):a.call(i,r)?s(e.get(r)):void(e!==i&&e.get(t))}function At(e,t=!1){const n=this.__v_raw,o=Zt(n),i=Zt(e);return t||(e!==i&&nt(o,0,e),nt(o,0,i)),e===i?n.has(e):n.has(e)||n.has(i)}function St(e,t=!1){return e=e.__v_raw,!t&&nt(Zt(e),0,Xe),Reflect.get(e,"size",e)}function Tt(e){e=Zt(e);const t=Zt(this);return xt(t).has.call(t,e)||(t.add(e),it(t,"add",e,e)),this}function Ct(e,t){t=Zt(t);const n=Zt(this),{has:o,get:i}=xt(n);let r=o.call(n,e);r||(e=Zt(e),r=o.call(n,e));const a=i.call(n,e);return n.set(e,t),r?L(t,a)&&it(n,"set",e,t):it(n,"add",e,t),this}function Et(e){const t=Zt(this),{has:n,get:o}=xt(t);let i=n.call(t,e);i||(e=Zt(e),i=n.call(t,e)),o&&o.call(t,e);const r=t.delete(e);return i&&it(t,"delete",e,void 0),r}function kt(){const e=Zt(this),t=0!==e.size,n=e.clear();return t&&it(e,"clear",void 0,void 0),n}function Pt(e,t){return function(n,o){const i=this,r=i.__v_raw,a=Zt(r),s=t?wt:e?nn:tn;return!e&&nt(a,0,Xe),r.forEach(((e,t)=>n.call(o,s(e),s(t),i)))}}function Bt(e,t,n){return function(...o){const i=this.__v_raw,r=Zt(i),a=h(r),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=i[e](...o),u=n?wt:t?nn:tn;return!t&&nt(r,0,l?Ye:Xe),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Mt(e){return function(...t){return"delete"!==e&&this}}function It(){const e={get(e){return _t(this,e)},get size(){return St(this)},has:At,add:Tt,set:Ct,delete:Et,clear:kt,forEach:Pt(!1,!1)},t={get(e){return _t(this,e,!1,!0)},get size(){return St(this)},has:At,add:Tt,set:Ct,delete:Et,clear:kt,forEach:Pt(!1,!0)},n={get(e){return _t(this,e,!0)},get size(){return St(this,!0)},has(e){return At.call(this,e,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Pt(!0,!1)},o={get(e){return _t(this,e,!0,!0)},get size(){return St(this,!0)},has(e){return At.call(this,e,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Pt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{e[i]=Bt(i,!1,!1),n[i]=Bt(i,!0,!1),t[i]=Bt(i,!1,!0),o[i]=Bt(i,!0,!0)})),[e,n,t,o]}const[Ot,Lt,Dt,zt]=It();function Rt(e,t){const n=t?e?zt:Dt:e?Lt:Ot;return(t,o,i)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(f(n,o)&&o in t?n:t,o,i)}const Nt={get:Rt(!1,!1)},jt={get:Rt(!1,!0)},Ft={get:Rt(!0,!1)},qt=new WeakMap,Vt=new WeakMap,Ht=new WeakMap,Qt=new WeakMap;function Ut(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>A(e).slice(8,-1))(e))}function Wt(e){return Jt(e)?e:Xt(e,!1,vt,Nt,qt)}function $t(e){return Xt(e,!0,yt,Ft,Ht)}function Xt(e,t,n,o,i){if(!w(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const r=i.get(e);if(r)return r;const a=Ut(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return i.set(e,s),s}function Yt(e){return Jt(e)?Yt(e.__v_raw):!(!e||!e.__v_isReactive)}function Jt(e){return!(!e||!e.__v_isReadonly)}function Gt(e){return!(!e||!e.__v_isShallow)}function Kt(e){return Yt(e)||Jt(e)}function Zt(e){const t=e&&e.__v_raw;return t?Zt(t):e}function en(e){return z(e,"__v_skip",!0),e}const tn=e=>w(e)?Wt(e):e,nn=e=>w(e)?$t(e):e;function on(e){Ke&&$e&&ot((e=Zt(e)).dep||(e.dep=qe()))}function rn(e,t){const n=(e=Zt(e)).dep;n&&rt(n)}function an(e){return!(!e||!0!==e.__v_isRef)}function sn(e){return cn(e,!1)}function ln(e){return cn(e,!0)}function cn(e,t){return an(e)?e:new un(e,t)}class un{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Zt(e),this._value=t?e:tn(e)}get value(){return on(this),this._value}set value(e){const t=this.__v_isShallow||Gt(e)||Jt(e);e=t?e:Zt(e),L(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:tn(e),rn(this))}}function dn(e){return an(e)?e.value:e}const fn={get:(e,t,n)=>dn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const i=e[t];return an(i)&&!an(n)?(i.value=n,!0):Reflect.set(e,t,n,o)}};function pn(e){return Yt(e)?e:new Proxy(e,fn)}class hn{constructor(e){this.dep=void 0,this.__v_isRef=!0;const{get:t,set:n}=e((()=>on(this)),(()=>rn(this)));this._get=t,this._set=n}get value(){return this._get()}set value(e){this._set(e)}}function gn(e){return new hn(e)}class mn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=Zt(this._object),t=this._key,null===(n=Qe.get(e))||void 0===n?void 0:n.get(t);var e,t,n}}function vn(e,t,n){const o=e[t];return an(o)?o:new mn(e,t,n)}var yn;class bn{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[yn]=!1,this._dirty=!0,this.effect=new Je(e,(()=>{this._dirty||(this._dirty=!0,rn(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Zt(this);return on(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function wn(e,t,n,o){let i;try{i=o?e(...o):e()}catch(r){_n(r,t,n)}return i}function xn(e,t,n,o){if(v(e)){const i=wn(e,t,n,o);return i&&x(i)&&i.catch((e=>{_n(e,t,n)})),i}const i=[];for(let r=0;r<e.length;r++)i.push(xn(e[r],t,n,o));return i}function _n(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const i=t.proxy,r=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,i,r))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void wn(a,null,10,[e,i,r])}}yn="__v_isReadonly";let An=!1,Sn=!1;const Tn=[];let Cn=0;const En=[];let kn=null,Pn=0;const Bn=Promise.resolve();let Mn=null;function In(e){const t=Mn||Bn;return e?t.then(this?e.bind(this):e):t}function On(e){Tn.length&&Tn.includes(e,An&&e.allowRecurse?Cn+1:Cn)||(null==e.id?Tn.push(e):Tn.splice(function(e){let t=Cn+1,n=Tn.length;for(;t<n;){const o=t+n>>>1;Rn(Tn[o])<e?t=o+1:n=o}return t}(e.id),0,e),Ln())}function Ln(){An||Sn||(Sn=!0,Mn=Bn.then(jn))}function Dn(e,t=(An?Cn+1:0)){for(;t<Tn.length;t++){const e=Tn[t];e&&e.pre&&(Tn.splice(t,1),t--,e())}}function zn(e){if(En.length){const e=[...new Set(En)];if(En.length=0,kn)return void kn.push(...e);for(kn=e,kn.sort(((e,t)=>Rn(e)-Rn(t))),Pn=0;Pn<kn.length;Pn++)kn[Pn]();kn=null,Pn=0}}const Rn=e=>null==e.id?1/0:e.id,Nn=(e,t)=>{const n=Rn(e)-Rn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function jn(e){Sn=!1,An=!0,Tn.sort(Nn);try{for(Cn=0;Cn<Tn.length;Cn++){const e=Tn[Cn];e&&!1!==e.active&&wn(e,null,14)}}finally{Cn=0,Tn.length=0,zn(),An=!1,Mn=null,(Tn.length||En.length)&&jn()}}function Fn(e,t,...n){if(e.isUnmounted)return;const i=e.vnode.props||o;let r=n;const a=t.startsWith("update:"),s=a&&t.slice(7);if(s&&s in i){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:a}=i[e]||o;a&&(r=n.map((e=>y(e)?e.trim():e))),t&&(r=n.map(R))}let l,c=i[l=O(t)]||i[l=O(P(t))];!c&&a&&(c=i[l=O(M(t))]),c&&xn(c,e,6,qn(e,c,r));const u=i[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,xn(u,e,6,qn(e,u,r))}}function qn(e,t,n){if(1!==n.length)return n;if(v(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&f(o,"type")&&f(o,"timeStamp")&&f(o,"target")&&f(o,"currentTarget")&&f(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Vn(e,t,n=!1){const o=t.emitsCache,i=o.get(e);if(void 0!==i)return i;const r=e.emits;let a={},s=!1;if(!v(e)){const o=e=>{const n=Vn(e,t,!0);n&&(s=!0,c(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return r||s?(p(r)?r.forEach((e=>a[e]=null)):c(a,r),w(e)&&o.set(e,a),a):(w(e)&&o.set(e,null),null)}function Hn(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,M(t))||f(e,t))}let Qn=null,Un=null;function Wn(e){const t=Qn;return Qn=e,Un=e&&e.type.__scopeId||null,t}function $n(e,t=Qn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Yi(-1);const i=Wn(t);let r;try{r=e(...n)}finally{Wn(i),o._d&&Yi(1)}return r};return o._n=!0,o._c=!0,o._d=!0,o}function Xn(e){const{type:t,vnode:n,proxy:o,withProxy:i,props:r,propsOptions:[a],slots:s,attrs:c,emit:u,render:d,renderCache:f,data:p,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const b=Wn(e);try{if(4&n.shapeFlag){const e=i||o;v=cr(d.call(e,e,f,r,h,p,g)),y=c}else{const e=t;0,v=cr(e.length>1?e(r,{attrs:c,slots:s,emit:u}):e(r,null)),y=t.props?c:Yn(c)}}catch(x){Ui.length=0,_n(x,e,1),v=rr(Hi)}let w=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=w;e.length&&7&t&&(a&&e.some(l)&&(y=Jn(y,a)),w=ar(w,y))}return n.dirs&&(w=ar(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),v=w,Wn(b),v}const Yn=e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t},Jn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Gn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let i=0;i<o.length;i++){const r=o[i];if(t[r]!==e[r]&&!Hn(n,r))return!0}return!1}const Kn=e=>e.__isSuspense;function Zn(e,t){if(mr){let n=mr.provides;const o=mr.parent&&mr.parent.provides;o===n&&(n=mr.provides=Object.create(o)),n[e]=t,"app"===mr.type.mpType&&mr.appContext.app.provide(e,t)}else;}function eo(e,t,n=!1){const o=mr||Qn;if(o){const i=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(i&&e in i)return i[e];if(arguments.length>1)return n&&v(t)?t.call(o.proxy):t}}function to(e,t){return io(e,null,t)}const no={};function oo(e,t,n){return io(e,t,n)}function io(e,t,{immediate:n,deep:i,flush:a,onTrack:s,onTrigger:l}=o){const c=Fe()===(null==mr?void 0:mr.scope)?mr:null;let d,f,h=!1,g=!1;if(an(e)?(d=()=>e.value,h=Gt(e)):Yt(e)?(d=()=>e,i=!0):p(e)?(g=!0,h=e.some((e=>Yt(e)||Gt(e))),d=()=>e.map((e=>an(e)?e.value:Yt(e)?so(e):v(e)?wn(e,c,2):void 0))):d=v(e)?t?()=>wn(e,c,2):()=>{if(!c||!c.isUnmounted)return f&&f(),xn(e,c,3,[y])}:r,t&&i){const e=d;d=()=>so(e())}let m,y=e=>{f=_.onStop=()=>{wn(e,c,4)}};if(xr){if(y=r,t?n&&xn(t,c,3,[d(),g?[]:void 0,y]):d(),"sync"!==a)return r;{const e=Mr();m=e.__watcherHandles||(e.__watcherHandles=[])}}let b=g?new Array(e.length).fill(no):no;const w=()=>{if(_.active)if(t){const e=_.run();(i||h||(g?e.some(((e,t)=>L(e,b[t]))):L(e,b)))&&(f&&f(),xn(t,c,3,[e,b===no?void 0:g&&b[0]===no?[]:b,y]),b=e)}else _.run()};let x;w.allowRecurse=!!t,"sync"===a?x=w:"post"===a?x=()=>Ri(w,c&&c.suspense):(w.pre=!0,c&&(w.id=c.uid),x=()=>On(w));const _=new Je(d,x);t?n?w():b=_.run():"post"===a?Ri(_.run.bind(_),c&&c.suspense):_.run();const A=()=>{_.stop(),c&&c.scope&&u(c.scope.effects,_)};return m&&m.push(A),A}function ro(e,t,n){const o=this.proxy,i=y(e)?e.includes(".")?ao(o,e):()=>o[e]:e.bind(o,o);let r;v(t)?r=t:(r=t.handler,n=t);const a=mr;yr(this);const s=io(i,r.bind(o),n);return a?yr(a):br(),s}function ao(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function so(e,t){if(!w(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),an(e))so(e.value,t);else if(p(e))for(let n=0;n<e.length;n++)so(e[n],t);else if(g(e)||h(e))e.forEach((e=>{so(e,t)}));else if(S(e))for(const n in e)so(e[n],t);return e}const lo=[Function,Array],co={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:lo,onEnter:lo,onAfterEnter:lo,onEnterCancelled:lo,onBeforeLeave:lo,onLeave:lo,onAfterLeave:lo,onLeaveCancelled:lo,onBeforeAppear:lo,onAppear:lo,onAfterAppear:lo,onAppearCancelled:lo},uo={name:"BaseTransition",props:co,setup(e,{slots:t}){const n=vr(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return jo((()=>{e.isMounted=!0})),Vo((()=>{e.isUnmounting=!0})),e}();let i;return()=>{const r=t.default&&vo(t.default(),!0);if(!r||!r.length)return;let a=r[0];if(r.length>1)for(const e of r)if(e.type!==Hi){a=e;break}const s=Zt(e),{mode:l}=s;if(o.isLeaving)return ho(a);const c=go(a);if(!c)return ho(a);const u=po(c,s,o,n);mo(c,u);const d=n.subTree,f=d&&go(d);let p=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===i?i=e:e!==i&&(i=e,p=!0)}if(f&&f.type!==Hi&&(!er(c,f)||p)){const e=po(f,s,o,n);if(mo(f,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},ho(a);"in-out"===l&&c.type!==Hi&&(e.delayLeave=(e,t,n)=>{fo(o,f)[String(f.key)]=f,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return a}}};function fo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function po(e,t,n,o){const{appear:i,mode:r,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,w=String(e.key),x=fo(n,e),_=(e,t)=>{e&&xn(e,o,9,t)},A=(e,t)=>{const n=t[1];_(e,t),p(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},S={mode:r,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!i)return;o=m||s}t._leaveCb&&t._leaveCb(!0);const r=x[w];r&&er(e,r)&&r.el._leaveCb&&r.el._leaveCb(),_(o,[t])},enter(e){let t=l,o=c,r=u;if(!n.isMounted){if(!i)return;t=v||l,o=y||c,r=b||u}let a=!1;const s=e._enterCb=t=>{a||(a=!0,_(t?r:o,[e]),S.delayedLeave&&S.delayedLeave(),e._enterCb=void 0)};t?A(t,[e,s]):s()},leave(t,o){const i=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();_(d,[t]);let r=!1;const a=t._leaveCb=n=>{r||(r=!0,o(),_(n?g:h,[t]),t._leaveCb=void 0,x[i]===e&&delete x[i])};x[i]=e,f?A(f,[t,a]):a()},clone:e=>po(e,t,n,o)};return S}function ho(e){if(_o(e))return(e=ar(e)).children=null,e}function go(e){return _o(e)?e.children?e.children[0]:void 0:e}function mo(e,t){6&e.shapeFlag&&e.component?mo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function vo(e,t=!1,n){let o=[],i=0;for(let r=0;r<e.length;r++){let a=e[r];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:r);a.type===qi?(128&a.patchFlag&&i++,o=o.concat(vo(a.children,t,s))):(t||a.type!==Hi)&&o.push(null!=s?ar(a,{key:s}):a)}if(i>1)for(let r=0;r<o.length;r++)o[r].patchFlag=-2;return o}function yo(e){return v(e)?{setup:e,name:e.name}:e}const bo=e=>!!e.type.__asyncLoader;function wo(e){v(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:i=200,timeout:r,suspensible:a=!0,onError:s}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),s)return new Promise(((t,n)=>{s(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return yo({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=mr;if(l)return()=>xo(l,e);const t=t=>{c=null,_n(t,e,13,!o)};if(a&&e.suspense||xr)return d().then((t=>()=>xo(t,e))).catch((e=>(t(e),()=>o?rr(o,{error:e}):null)));const s=sn(!1),u=sn(),f=sn(!!i);return i&&setTimeout((()=>{f.value=!1}),i),null!=r&&setTimeout((()=>{if(!s.value&&!u.value){const e=new Error(`Async component timed out after ${r}ms.`);t(e),u.value=e}}),r),d().then((()=>{s.value=!0,e.parent&&_o(e.parent.vnode)&&On(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>s.value&&l?xo(l,e):u.value&&o?rr(o,{error:u.value}):n&&!f.value?rr(n):void 0}})}function xo(e,t){const{ref:n,props:o,children:i,ce:r}=t.vnode,a=rr(e,o,i);return a.ref=n,a.ce=r,delete t.vnode.ce,a}const _o=e=>e.type.__isKeepAlive;class Ao{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,i=t.get(e);if(i)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return i}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const So={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=vr(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const i=e.cache||new Ao(e.max);i.pruneCacheEntry=a;let r=null;function a(t){var o;!r||!er(t,r)||"key"===e.matchBy&&t.key!==r.key?(Mo(o=t),u(o,n,s,!0)):r&&Mo(r)}const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function p(t){i.forEach(((n,o)=>{const r=Oo(n,e.matchBy);!r||t&&t(r)||(i.delete(o),a(n))}))}o.activate=(e,t,n,o,i)=>{const r=e.component;if(r.ba){const e=r.isDeactivated;r.isDeactivated=!1,D(r.ba),r.isDeactivated=e}c(e,t,n,0,s),l(r.vnode,e,t,n,r,s,o,e.slotScopeIds,i),Ri((()=>{r.isDeactivated=!1,r.a&&D(r.a);const t=e.props&&e.props.onVnodeMounted;t&&pr(t,r.parent,e)}),s)},o.deactivate=e=>{const t=e.component;t.bda&&Lo(t.bda),c(e,f,null,1,s),Ri((()=>{t.bda&&Do(t.bda),t.da&&D(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&pr(n,t.parent,e),t.isDeactivated=!0}),s)},oo((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&p((t=>Co(e,t))),t&&p((e=>!Co(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&i.set(h,Io(n.subTree))};return jo(g),qo(g),Vo((()=>{i.forEach(((t,o)=>{i.delete(o),a(t);const{subTree:r,suspense:s}=n,l=Io(r);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&D(l.component.bda),Mo(l);const e=l.component.da;e&&Ri(e,s)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return r=null,n;if(!Zi(o)||!(4&o.shapeFlag)&&!Kn(o.type))return r=null,o;let a=Io(o);const s=a.type,l=Oo(a,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Co(c,l))||u&&l&&Co(u,l))return r=a,o;const d=null==a.key?s:a.key,f=i.get(d);return a.el&&(a=ar(a),Kn(o.type)&&(o.ssContent=a)),h=d,f&&(a.el=f.el,a.component=f.component,a.transition&&mo(a,a.transition),a.shapeFlag|=512),a.shapeFlag|=256,r=a,Kn(o.type)?o:a}}},To=So;function Co(e,t){return p(e)?e.some((e=>Co(e,t))):y(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function Eo(e,t){Po(e,"a",t)}function ko(e,t){Po(e,"da",t)}function Po(e,t,n=mr){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,zo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)_o(e.parent.vnode)&&Bo(o,t,n,e),e=e.parent}}function Bo(e,t,n,o){const i=zo(t,e,o,!0);Ho((()=>{u(o[t],i)}),n)}function Mo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Io(e){return Kn(e.type)?e.ssContent:e}function Oo(e,t){if("name"===t){const t=e.type;return Cr(bo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Lo(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Do(e){e.forEach((e=>e.__called=!1))}function zo(e,t,n=mr,o=!1){if(n){if(i=e,ke.indexOf(i)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return Pe.indexOf(e)>-1}(e))){const o=n.proxy;xn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const r=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;et(),yr(n);const i=xn(t,n,e,o);return br(),tt(),i});return o?r.unshift(a):r.push(a),a}var i}const Ro=e=>(t,n=mr)=>(!xr||"sp"===e)&&zo(e,((...e)=>t(...e)),n),No=Ro("bm"),jo=Ro("m"),Fo=Ro("bu"),qo=Ro("u"),Vo=Ro("bum"),Ho=Ro("um"),Qo=Ro("sp"),Uo=Ro("rtg"),Wo=Ro("rtc");function $o(e,t=mr){zo("ec",e,t)}function Xo(e,t){const n=Qn;if(null===n)return e;const i=Tr(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let a=0;a<t.length;a++){let[e,n,s,l=o]=t[a];e&&(v(e)&&(e={mounted:e,updated:e}),e.deep&&so(n),r.push({dir:e,instance:i,value:n,oldValue:void 0,arg:s,modifiers:l}))}return e}function Yo(e,t,n,o){const i=e.dirs,r=t&&t.dirs;for(let a=0;a<i.length;a++){const s=i[a];r&&(s.oldValue=r[a].value);let l=s.dir[o];l&&(et(),xn(l,n,8,[e.el,s,e,t]),tt())}}function Jo(e,t){return Zo("components",e,!0,t)||e}const Go=Symbol();function Ko(e){return y(e)?Zo("components",e,!1)||e:e||Go}function Zo(e,t,n=!0,o=!1){const i=Qn||mr;if(i){const n=i.type;if("components"===e){const e=Cr(n,!1);if(e&&(e===t||e===P(t)||e===I(P(t))))return n}const r=ei(i[e]||n[e],t)||ei(i.appContext[e],t);return!r&&o?n:r}}function ei(e,t){return e&&(e[t]||e[P(t)]||e[I(P(t))])}function ti(e,t,n,o){let i;const r=n&&n[o];if(p(e)||y(e)){i=new Array(e.length);for(let n=0,o=e.length;n<o;n++)i[n]=t(e[n],n,void 0,r&&r[n])}else if("number"==typeof e){i=new Array(e);for(let n=0;n<e;n++)i[n]=t(n+1,n,void 0,r&&r[n])}else if(w(e))if(e[Symbol.iterator])i=Array.from(e,((e,n)=>t(e,n,void 0,r&&r[n])));else{const n=Object.keys(e);i=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];i[o]=t(e[a],a,o,r&&r[o])}}else i=[];return n&&(n[o]=i),i}function ni(e,t){for(let n=0;n<t.length;n++){const o=t[n];if(p(o))for(let t=0;t<o.length;t++)e[o[t].name]=o[t].fn;else o&&(e[o.name]=o.key?(...e)=>{const t=o.fn(...e);return t&&(t.key=o.key),t}:o.fn)}return e}function oi(e,t,n={},o,i){if(Qn.isCE||Qn.parent&&bo(Qn.parent)&&Qn.parent.isCE)return"default"!==t&&(n.name=t),rr("slot",n,o&&o());let r=e[t];r&&r._c&&(r._d=!1),$i();const a=r&&ii(r(n)),s=Ki(qi,{key:n.key||a&&a.key||`_${t}`},a||(o?o():[]),a&&1===e._?64:-2);return!i&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),r&&r._c&&(r._d=!0),s}function ii(e){return e.some((e=>!Zi(e)||e.type!==Hi&&!(e.type===qi&&!ii(e.children))))?e:null}const ri=e=>{if(!e)return null;if(wr(e)){return Tr(e)||e.proxy}return ri(e.parent)},ai=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ri(e.parent),$root:e=>ri(e.root),$emit:e=>e.emit,$options:e=>pi(e),$forceUpdate:e=>e.f||(e.f=()=>{On(e.update)}),$nextTick:e=>e.n||(e.n=In.bind(e.proxy)),$watch:e=>ro.bind(e)}),si=(e,t)=>e!==o&&!e.__isScriptSetup&&f(e,t),li={get({_:e},t){const{ctx:n,setupState:i,data:r,props:a,accessCache:s,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=s[t];if(void 0!==l)switch(l){case 1:return i[t];case 2:return r[t];case 4:return n[t];case 3:return a[t]}else{if(si(i,t))return s[t]=1,i[t];if(r!==o&&f(r,t))return s[t]=2,r[t];if((u=e.propsOptions[0])&&f(u,t))return s[t]=3,a[t];if(n!==o&&f(n,t))return s[t]=4,n[t];ci&&(s[t]=0)}}const d=ai[t];let p,h;return d?("$attrs"===t&&nt(e,0,t),d(e)):(p=l.__cssModules)&&(p=p[t])?p:n!==o&&f(n,t)?(s[t]=4,n[t]):(h=c.config.globalProperties,f(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:i,setupState:r,ctx:a}=e;return si(r,t)?(r[t]=n,!0):i!==o&&f(i,t)?(i[t]=n,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(a[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:i,appContext:r,propsOptions:a}},s){let l;return!!n[s]||e!==o&&f(e,s)||si(t,s)||(l=a[0])&&f(l,s)||f(i,s)||f(ai,s)||f(r.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let ci=!0;function ui(e){const t=pi(e),n=e.proxy,o=e.ctx;ci=!1,t.beforeCreate&&di(t.beforeCreate,e,"bc");const{data:i,computed:a,methods:s,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:h,beforeUpdate:g,updated:m,activated:y,deactivated:b,beforeDestroy:x,beforeUnmount:_,destroyed:A,unmounted:S,render:T,renderTracked:C,renderTriggered:E,errorCaptured:k,serverPrefetch:P,expose:B,inheritAttrs:M,components:I,directives:O,filters:L}=t;if(u&&function(e,t,n=r,o=!1){p(e)&&(e=vi(e));for(const i in e){const n=e[i];let r;r=w(n)?"default"in n?eo(n.from||i,n.default,!0):eo(n.from||i):eo(n),an(r)&&o?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[i]=r}}(u,o,null,e.appContext.config.unwrapInjectedRef),s)for(const r in s){const e=s[r];v(e)&&(o[r]=e.bind(n))}if(i){const t=i.call(n,n);w(t)&&(e.data=Wt(t))}if(ci=!0,a)for(const p in a){const e=a[p],t=v(e)?e.bind(n,n):v(e.get)?e.get.bind(n,n):r,i=!v(e)&&v(e.set)?e.set.bind(n):r,s=Er({get:t,set:i});Object.defineProperty(o,p,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const r in l)fi(l[r],o,n,r);if(c){const e=v(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{Zn(t,e[t])}))}function D(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&di(d,e,"c"),D(No,f),D(jo,h),D(Fo,g),D(qo,m),D(Eo,y),D(ko,b),D($o,k),D(Wo,C),D(Uo,E),D(Vo,_),D(Ho,S),D(Qo,P),p(B))if(B.length){const t=e.exposed||(e.exposed={});B.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});T&&e.render===r&&(e.render=T),null!=M&&(e.inheritAttrs=M),I&&(e.components=I),O&&(e.directives=O);const z=e.appContext.config.globalProperties.$applyOptions;z&&z(t,e,n)}function di(e,t,n){xn(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function fi(e,t,n,o){const i=o.includes(".")?ao(n,o):()=>n[o];if(y(e)){const n=t[e];v(n)&&oo(i,n)}else if(v(e))oo(i,e.bind(n));else if(w(e))if(p(e))e.forEach((e=>fi(e,t,n,o)));else{const o=v(e.handler)?e.handler.bind(n):t[e.handler];v(o)&&oo(i,o,e)}}function pi(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:a}}=e.appContext,s=r.get(t);let l;return s?l=s:i.length||n||o?(l={},i.length&&i.forEach((e=>hi(l,e,a,!0))),hi(l,t,a)):l=t,w(t)&&r.set(t,l),l}function hi(e,t,n,o=!1){const{mixins:i,extends:r}=t;r&&hi(e,r,n,!0),i&&i.forEach((t=>hi(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=gi[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const gi={data:mi,props:bi,emits:bi,methods:bi,computed:bi,beforeCreate:yi,created:yi,beforeMount:yi,mounted:yi,beforeUpdate:yi,updated:yi,beforeDestroy:yi,beforeUnmount:yi,destroyed:yi,unmounted:yi,activated:yi,deactivated:yi,errorCaptured:yi,serverPrefetch:yi,components:bi,directives:bi,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=yi(e[o],t[o]);return n},provide:mi,inject:function(e,t){return bi(vi(e),vi(t))}};function mi(e,t){return t?e?function(){return c(v(e)?e.call(this,this):e,v(t)?t.call(this,this):t)}:t:e}function vi(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function yi(e,t){return e?[...new Set([].concat(e,t))]:t}function bi(e,t){return e?c(c(Object.create(null),e),t):t}function wi(e,t,n,o=!1){const i={},r={};z(r,tr,1),e.propsDefaults=Object.create(null),xi(e,t,i,r);for(const a in e.propsOptions[0])a in i||(i[a]=void 0);n?e.props=o?i:Xt(i,!1,bt,jt,Vt):e.type.props?e.props=i:e.props=r,e.attrs=r}function xi(e,t,n,i){const[r,a]=e.propsOptions;let s,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;r&&f(r,u=P(o))?a&&a.includes(u)?(s||(s={}))[u]=c:n[u]=c:Hn(e.emitsOptions,o)||o in i&&c===i[o]||(i[o]=c,l=!0)}if(a){const t=Zt(n),i=s||o;for(let o=0;o<a.length;o++){const s=a[o];n[s]=_i(r,t,s,i[s],e,!f(i,s))}}return l}function _i(e,t,n,o,i,r){const a=e[n];if(null!=a){const e=f(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&v(e)){const{propsDefaults:r}=i;n in r?o=r[n]:(yr(i),o=r[n]=e.call(null,t),br())}else o=e}a[0]&&(r&&!e?o=!1:!a[1]||""!==o&&o!==M(n)||(o=!0))}return o}function Ai(e,t,n=!1){const r=t.propsCache,a=r.get(e);if(a)return a;const s=e.props,l={},u=[];let d=!1;if(!v(e)){const o=e=>{d=!0;const[n,o]=Ai(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!s&&!d)return w(e)&&r.set(e,i),i;if(p(s))for(let i=0;i<s.length;i++){const e=P(s[i]);Si(e)&&(l[e]=o)}else if(s)for(const o in s){const e=P(o);if(Si(e)){const t=s[o],n=l[e]=p(t)||v(t)?{type:t}:Object.assign({},t);if(n){const t=Ei(Boolean,n.type),o=Ei(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||f(n,"default"))&&u.push(e)}}}const h=[l,u];return w(e)&&r.set(e,h),h}function Si(e){return"$"!==e[0]}function Ti(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function Ci(e,t){return Ti(e)===Ti(t)}function Ei(e,t){return p(t)?t.findIndex((t=>Ci(t,e))):v(t)&&Ci(t,e)?0:-1}const ki=e=>"_"===e[0]||"$stable"===e,Pi=e=>p(e)?e.map(cr):[cr(e)],Bi=(e,t,n)=>{if(t._n)return t;const o=$n(((...e)=>Pi(t(...e))),n);return o._c=!1,o},Mi=(e,t,n)=>{const o=e._ctx;for(const i in e){if(ki(i))continue;const n=e[i];if(v(n))t[i]=Bi(0,n,o);else if(null!=n){const e=Pi(n);t[i]=()=>e}}},Ii=(e,t)=>{const n=Pi(t);e.slots.default=()=>n};function Oi(){return{app:null,config:{isNativeTag:a,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Li=0;function Di(e,t){return function(n,o=null){v(n)||(n=Object.assign({},n)),null==o||w(o)||(o=null);const i=Oi(),r=new Set;let a=!1;const s=i.app={_uid:Li++,_component:n,_props:o,_container:null,_context:i,_instance:null,version:Ir,get config(){return i.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&v(e.install)?(r.add(e),e.install(s,...t)):v(e)&&(r.add(e),e(s,...t))),s),mixin:e=>(i.mixins.includes(e)||i.mixins.push(e),s),component:(e,t)=>t?(i.components[e]=t,s):i.components[e],directive:(e,t)=>t?(i.directives[e]=t,s):i.directives[e],mount(r,l,c){if(!a){const u=rr(n,o);return u.appContext=i,l&&t?t(u,r):e(u,r,c),a=!0,s._container=r,r.__vue_app__=s,s._instance=u.component,Tr(u.component)||u.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(i.provides[e]=t,s)};return s}}function zi(e,t,n,i,r=!1){if(p(e))return void e.forEach(((e,o)=>zi(e,t&&(p(t)?t[o]:t),n,i,r)));if(bo(i)&&!r)return;const a=4&i.shapeFlag?Tr(i.component)||i.component.proxy:i.el,s=r?null:a,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(y(d)?(h[d]=null,f(g,d)&&(g[d]=null)):an(d)&&(d.value=null)),v(c))wn(c,l,12,[s,h]);else{const t=y(c),o=an(c);if(t||o){const i=()=>{if(e.f){const n=t?f(g,c)?g[c]:h[c]:c.value;r?p(n)&&u(n,a):p(n)?n.includes(a)||n.push(a):t?(h[c]=[a],f(g,c)&&(g[c]=h[c])):(c.value=[a],e.k&&(h[e.k]=c.value))}else t?(h[c]=s,f(g,c)&&(g[c]=s)):o&&(c.value=s,e.k&&(h[e.k]=s))};s?(i.id=-1,Ri(i,n)):i()}}}const Ri=function(e,t){var n;t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):(p(n=e)?En.push(...n):kn&&kn.includes(n,n.allowRecurse?Pn+1:Pn)||En.push(n),Ln())};function Ni(e){return function(e,t){(N||(N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:a,patchProp:s,forcePatchProp:l,createElement:u,createText:d,createComment:p,setText:h,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=r,insertStaticContent:b}=e,w=(e,t,n,o=null,i=null,r=null,a=!1,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!er(e,t)&&(o=te(e),J(e,i,r,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Vi:_(e,t,n,o);break;case Hi:A(e,t,n,o);break;case Qi:null==e&&S(t,n,o,a);break;case qi:F(e,t,n,o,i,r,a,s,l);break;default:1&d?k(e,t,n,o,i,r,a,s,l):6&d?q(e,t,n,o,i,r,a,s,l):(64&d||128&d)&&c.process(e,t,n,o,i,r,a,s,l,oe)}null!=u&&i&&zi(u,e&&e.ref,r,t||e,!t)},_=(e,t,o,i)=>{if(null==e)n(t.el=d(t.children),o,i);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},A=(e,t,o,i)=>{null==e?n(t.el=p(t.children||""),o,i):t.el=e.el},S=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},T=({el:e,anchor:t},o,i)=>{let r;for(;e&&e!==t;)r=v(e),n(e,o,i),e=r;n(t,o,i)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),a(e),e=n;a(t)},k=(e,t,n,o,i,r,a,s,l)=>{a=a||"svg"===t.type,null==e?B(t,n,o,i,r,a,s,l):L(e,t,i,r,a,s,l)},B=(e,t,o,i,r,a,l,c)=>{let d,f;const{type:p,props:h,shapeFlag:m,transition:v,dirs:y}=e;if(d=e.el=u(e.type,a,h&&h.is,h),8&m?g(d,e.children):16&m&&O(e.children,d,null,i,r,a&&"foreignObject"!==p,l,c),y&&Yo(e,null,i,"created"),I(d,e,e.scopeId,l,i),h){for(const t in h)"value"===t||C(t)||s(d,t,null,h[t],a,e.children,i,r,ee);"value"in h&&s(d,"value",null,h.value),(f=h.onVnodeBeforeMount)&&pr(f,i,e)}Object.defineProperty(d,"__vueParentComponent",{value:i,enumerable:!1}),y&&Yo(e,null,i,"beforeMount");const b=(!r||r&&!r.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(d),n(d,t,o),((f=h&&h.onVnodeMounted)||b||y)&&Ri((()=>{f&&pr(f,i,e),b&&v.enter(d),y&&Yo(e,null,i,"mounted")}),r)},I=(e,t,n,o,i)=>{if(n&&y(e,n),o)for(let r=0;r<o.length;r++)y(e,o[r]);if(i){if(t===i.subTree){const t=i.vnode;I(e,t,t.scopeId,t.slotScopeIds,i.parent)}}},O=(e,t,n,o,i,r,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?ur(e[c]):cr(e[c]);w(null,l,t,n,o,i,r,a,s)}},L=(e,t,n,i,r,a,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:p}=t;d|=16&e.patchFlag;const h=e.props||o,m=t.props||o;let v;n&&ji(n,!1),(v=m.onVnodeBeforeUpdate)&&pr(v,n,t,e),p&&Yo(t,e,n,"beforeUpdate"),n&&ji(n,!0);const y=r&&"foreignObject"!==t.type;if(f?R(e.dynamicChildren,f,u,n,i,y,a):c||W(e,t,u,null,n,i,y,a,!1),d>0){if(16&d)j(u,t,h,m,n,i,r);else if(2&d&&h.class!==m.class&&s(u,"class",null,m.class,r),4&d&&s(u,"style",h.style,m.style,r),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const a=o[t],c=h[a],d=m[a];(d!==c||"value"===a||l&&l(u,a))&&s(u,a,c,d,r,e.children,n,i,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=f||j(u,t,h,m,n,i,r);((v=m.onVnodeUpdated)||p)&&Ri((()=>{v&&pr(v,n,t,e),p&&Yo(t,e,n,"updated")}),i)},R=(e,t,n,o,i,r,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===qi||!er(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,i,r,a,!0)}},j=(e,t,n,i,r,a,c)=>{if(n!==i){if(n!==o)for(const o in n)C(o)||o in i||s(e,o,n[o],null,c,t.children,r,a,ee);for(const o in i){if(C(o))continue;const u=i[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&s(e,o,d,u,c,t.children,r,a,ee)}"value"in i&&s(e,"value",n.value,i.value)}},F=(e,t,o,i,r,a,s,l,c)=>{const u=t.el=e?e.el:d(""),f=t.anchor=e?e.anchor:d("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,i),n(f,o,i),O(t.children||[],o,f,r,a,s,l,c)):p>0&&64&p&&h&&e.dynamicChildren?(R(e.dynamicChildren,h,o,r,a,s,l),(null!=t.key||r&&t===r.subTree)&&Fi(e,t,!0)):W(e,t,o,f,r,a,s,l,c)},q=(e,t,n,o,i,r,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?i.ctx.activate(t,n,o,a,l):V(t,n,o,i,r,a,l):H(e,t,l)},V=(e,t,n,i,r,a,s)=>{const l=e.component=function(e,t,n){const i=e.type,r=(t?t.appContext:e.appContext)||hr,a={uid:gr++,vnode:e,type:i,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ne(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ai(i,r),emitsOptions:Vn(i,r),emit:null,emitted:null,propsDefaults:o,inheritAttrs:i.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=Fn.bind(null,a),a.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(a);return a}(e,i,r);if(_o(e)&&(l.ctx.renderer=oe),function(e,t=!1){xr=t;const{props:n,children:o}=e.vnode,i=wr(e);wi(e,n,i,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Zt(t),z(t,"_",n)):Mi(t,e.slots={})}else e.slots={},t&&Ii(e,t);z(e.slots,tr,1)})(e,o);const r=i?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=en(new Proxy(e.ctx,li));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?Sr(e):null;yr(e),et();const i=wn(o,e,0,[e.props,n]);if(tt(),br(),x(i)){if(i.then(br,br),t)return i.then((n=>{_r(e,n,t)})).catch((t=>{_n(t,e,0)}));e.asyncDep=i}else _r(e,i,t)}else Ar(e,t)}(e,t):void 0;xr=!1}(l),l.asyncDep){if(r&&r.registerDep(l,Q),!e.el){const e=l.subTree=rr(Hi);A(null,e,t,n)}}else Q(l,e,t,n,r,a,s)},H=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:i,component:r}=e,{props:a,children:s,patchFlag:l}=t,c=r.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!i&&!s||s&&s.$stable)||o!==a&&(o?!a||Gn(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?Gn(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!Hn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void U(o,t,n);o.next=t,function(e){const t=Tn.indexOf(e);t>Cn&&Tn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},Q=(e,t,n,o,i,r,a)=>{const s=()=>{if(e.isMounted){let t,{next:n,bu:o,u:s,parent:l,vnode:c}=e,u=n;ji(e,!1),n?(n.el=c.el,U(e,n,a)):n=c,o&&D(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&pr(t,l,n,c),ji(e,!0);const d=Xn(e),f=e.subTree;e.subTree=d,w(f,d,m(f.el),te(f),e,i,r),n.el=d.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,d.el),s&&Ri(s,i),(t=n.props&&n.props.onVnodeUpdated)&&Ri((()=>pr(t,l,n,c)),i)}else{let a;const{el:s,props:l}=t,{bm:c,m:u,parent:d}=e,f=bo(t);if(ji(e,!1),c&&D(c),!f&&(a=l&&l.onVnodeBeforeMount)&&pr(a,d,t),ji(e,!0),s&&re){const n=()=>{e.subTree=Xn(e),re(s,e.subTree,e,i,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const a=e.subTree=Xn(e);w(null,a,n,o,e,i,r),t.el=a.el}if(u&&Ri(u,i),!f&&(a=l&&l.onVnodeMounted)){const e=t;Ri((()=>pr(a,d,e)),i)}const{ba:p,a:h}=e;(256&t.shapeFlag||d&&bo(d.vnode)&&256&d.vnode.shapeFlag)&&(p&&Lo(p),h&&Ri(h,i),p&&Ri((()=>Do(p)),i)),e.isMounted=!0,t=n=o=null}},l=e.effect=new Je(s,(()=>On(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,ji(e,!0),c()},U=(e,t,n)=>{t.component=e;const i=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:i,attrs:r,vnode:{patchFlag:a}}=e,s=Zt(i),[l]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;xi(e,t,i,r)&&(c=!0);for(const r in s)t&&(f(t,r)||(o=M(r))!==r&&f(t,o))||(l?!n||void 0===n[r]&&void 0===n[o]||(i[r]=_i(l,s,r,void 0,e,!0)):delete i[r]);if(r!==s)for(const e in r)t&&f(t,e)||(delete r[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(Hn(e.emitsOptions,a))continue;const u=t[a];if(l)if(f(r,a))u!==r[a]&&(r[a]=u,c=!0);else{const t=P(a);i[t]=_i(l,s,t,u,e,!1)}else u!==r[a]&&(r[a]=u,c=!0)}}c&&it(e,"set","$attrs")}(e,t.props,i,n),((e,t,n)=>{const{vnode:i,slots:r}=e;let a=!0,s=o;if(32&i.shapeFlag){const e=t._;e?n&&1===e?a=!1:(c(r,t),n||1!==e||delete r._):(a=!t.$stable,Mi(t,r)),s=t}else t&&(Ii(e,t),s={default:1});if(a)for(const o in r)ki(o)||o in s||delete r[o]})(e,t.children,n),et(),Dn(),tt()},W=(e,t,n,o,i,r,a,s,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void X(c,d,n,o,i,r,a,s,l);if(256&f)return void $(c,d,n,o,i,r,a,s,l)}8&p?(16&u&&ee(c,i,r),d!==c&&g(n,d)):16&u?16&p?X(c,d,n,o,i,r,a,s,l):ee(c,i,r,!0):(8&u&&g(n,""),16&p&&O(d,n,o,i,r,a,s,l))},$=(e,t,n,o,r,a,s,l,c)=>{t=t||i;const u=(e=e||i).length,d=t.length,f=Math.min(u,d);let p;for(p=0;p<f;p++){const o=t[p]=c?ur(t[p]):cr(t[p]);w(e[p],o,n,null,r,a,s,l,c)}u>d?ee(e,r,a,!0,!1,f):O(t,n,o,r,a,s,l,c,f)},X=(e,t,n,o,r,a,s,l,c)=>{let u=0;const d=t.length;let f=e.length-1,p=d-1;for(;u<=f&&u<=p;){const o=e[u],i=t[u]=c?ur(t[u]):cr(t[u]);if(!er(o,i))break;w(o,i,n,null,r,a,s,l,c),u++}for(;u<=f&&u<=p;){const o=e[f],i=t[p]=c?ur(t[p]):cr(t[p]);if(!er(o,i))break;w(o,i,n,null,r,a,s,l,c),f--,p--}if(u>f){if(u<=p){const e=p+1,i=e<d?t[e].el:o;for(;u<=p;)w(null,t[u]=c?ur(t[u]):cr(t[u]),n,i,r,a,s,l,c),u++}}else if(u>p)for(;u<=f;)J(e[u],r,a,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=p;u++){const e=t[u]=c?ur(t[u]):cr(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const b=p-g+1;let x=!1,_=0;const A=new Array(b);for(u=0;u<b;u++)A[u]=0;for(u=h;u<=f;u++){const o=e[u];if(y>=b){J(o,r,a,!0);continue}let i;if(null!=o.key)i=m.get(o.key);else for(v=g;v<=p;v++)if(0===A[v-g]&&er(o,t[v])){i=v;break}void 0===i?J(o,r,a,!0):(A[i-g]=u+1,i>=_?_=i:x=!0,w(o,t[i],n,null,r,a,s,l,c),y++)}const S=x?function(e){const t=e.slice(),n=[0];let o,i,r,a,s;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(i=n[n.length-1],e[i]<l){t[o]=i,n.push(o);continue}for(r=0,a=n.length-1;r<a;)s=r+a>>1,e[n[s]]<l?r=s+1:a=s;l<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}r=n.length,a=n[r-1];for(;r-- >0;)n[r]=a,a=t[a];return n}(A):i;for(v=S.length-1,u=b-1;u>=0;u--){const e=g+u,i=t[e],f=e+1<d?t[e+1].el:o;0===A[u]?w(null,i,n,f,r,a,s,l,c):x&&(v<0||u!==S[v]?Y(i,n,f,2):v--)}}},Y=(e,t,o,i,r=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void Y(e.component.subTree,t,o,i);if(128&u)return void e.suspense.move(t,o,i);if(64&u)return void s.move(e,t,o,oe);if(s===qi){n(a,t,o);for(let e=0;e<c.length;e++)Y(c[e],t,o,i);return void n(e.anchor,t,o)}if(s===Qi)return void T(e,t,o);if(2!==i&&1&u&&l)if(0===i)l.beforeEnter(a),n(a,t,o),Ri((()=>l.enter(a)),r);else{const{leave:e,delayLeave:i,afterLeave:r}=l,s=()=>n(a,t,o),c=()=>{e(a,(()=>{s(),r&&r()}))};i?i(a,s,c):c()}else n(a,t,o)},J=(e,t,n,o=!1,i=!1)=>{const{type:r,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=s&&zi(s,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!bo(e);let g;if(h&&(g=a&&a.onVnodeBeforeUnmount)&&pr(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&Yo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,i,oe,o):c&&(r!==qi||d>0&&64&d)?ee(c,t,n,!1,!0):(r===qi&&384&d||!i&&16&u)&&ee(l,t,n),o&&G(e)}(h&&(g=a&&a.onVnodeUnmounted)||p)&&Ri((()=>{g&&pr(g,t,e),p&&Yo(e,null,t,"unmounted")}),n)},G=e=>{const{type:t,el:n,anchor:o,transition:i}=e;if(t===qi)return void K(n,o);if(t===Qi)return void E(e);const r=()=>{a(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,a=()=>t(n,r);o?o(e.el,r,a):a()}else r()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),a(e),e=n;a(t)},Z=(e,t,n)=>{const{bum:o,scope:i,update:r,subTree:a,um:s}=e;o&&D(o),i.stop(),r&&(r.active=!1,J(a,e,t,n)),s&&Ri(s,t),Ri((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,i=!1,r=0)=>{for(let a=r;a<e.length;a++)J(e[a],t,n,o,i)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&J(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),Dn(),zn(),t._vnode=e},oe={p:w,um:J,m:Y,r:G,mt:V,mc:O,pc:W,pbc:R,n:te,o:e};let ie,re;t&&([ie,re]=t(oe));return{render:ne,hydrate:ie,createApp:Di(ne,ie)}}(e)}function ji({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Fi(e,t,n=!1){const o=e.children,i=t.children;if(p(o)&&p(i))for(let r=0;r<o.length;r++){const e=o[r];let t=i[r];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=i[r]=ur(i[r]),t.el=e.el),n||Fi(e,t)),t.type===Vi&&(t.el=e.el)}}const qi=Symbol(void 0),Vi=Symbol(void 0),Hi=Symbol(void 0),Qi=Symbol(void 0),Ui=[];let Wi=null;function $i(e=!1){Ui.push(Wi=e?null:[])}let Xi=1;function Yi(e){Xi+=e}function Ji(e){return e.dynamicChildren=Xi>0?Wi||i:null,Ui.pop(),Wi=Ui[Ui.length-1]||null,Xi>0&&Wi&&Wi.push(e),e}function Gi(e,t,n,o,i,r){return Ji(ir(e,t,n,o,i,r,!0))}function Ki(e,t,n,o,i){return Ji(rr(e,t,n,o,i,!0))}function Zi(e){return!!e&&!0===e.__v_isVNode}function er(e,t){return e.type===t.type&&e.key===t.key}const tr="__vInternal",nr=({key:e})=>null!=e?e:null,or=({ref:e,ref_key:t,ref_for:n})=>null!=e?y(e)||an(e)||v(e)?{i:Qn,r:e,k:t,f:!!n}:e:null;function ir(e,t=null,n=null,o=0,i=null,r=(e===qi?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&nr(t),ref:t&&or(t),scopeId:Un,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Qn};return s?(dr(l,n),128&r&&e.normalize(l)):n&&(l.shapeFlag|=y(n)?8:16),Xi>0&&!a&&Wi&&(l.patchFlag>0||6&r)&&32!==l.patchFlag&&Wi.push(l),l}const rr=function(e,t=null,n=null,o=0,i=null,r=!1){e&&e!==Go||(e=Hi);if(Zi(e)){const o=ar(e,t,!0);return n&&dr(o,n),Xi>0&&!r&&Wi&&(6&o.shapeFlag?Wi[Wi.indexOf(e)]=o:Wi.push(o)),o.patchFlag|=-2,o}a=e,v(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?Kt(e)||tr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!y(e)&&(t.class=ce(e)),w(n)&&(Kt(n)&&!p(n)&&(n=c({},n)),t.style=le(n))}const s=y(e)?1:Kn(e)?128:(e=>e.__isTeleport)(e)?64:w(e)?4:v(e)?2:0;return ir(e,t,n,o,i,s,r,!0)};function ar(e,t,n=!1){const{props:o,ref:i,patchFlag:r,children:a}=e,s=t?fr(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&nr(s),ref:t&&t.ref?n&&i?p(i)?i.concat(or(t)):[i,or(t)]:or(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==qi?-1===r?16:16|r:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ar(e.ssContent),ssFallback:e.ssFallback&&ar(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function sr(e=" ",t=0){return rr(Vi,null,e,t)}function lr(e="",t=!1){return t?($i(),Ki(Hi,null,e)):rr(Hi,null,e)}function cr(e){return null==e||"boolean"==typeof e?rr(Hi):p(e)?rr(qi,null,e.slice()):"object"==typeof e?ur(e):rr(Vi,null,String(e))}function ur(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ar(e)}function dr(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),dr(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||tr in t?3===o&&Qn&&(1===Qn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Qn}}else v(t)?(t={default:t,_ctx:Qn},n=32):(t=String(t),64&o?(n=16,t=[sr(t)]):n=8);e.children=t,e.shapeFlag|=n}function fr(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=ce([t.class,o.class]));else if("style"===e)t.style=le([t.style,o.style]);else if(s(e)){const n=t[e],i=o[e];!i||n===i||p(n)&&n.includes(i)||(t[e]=n?[].concat(n,i):i)}else""!==e&&(t[e]=o[e])}return t}function pr(e,t,n,o=null){xn(e,t,7,[n,o])}const hr=Oi();let gr=0;let mr=null;const vr=()=>mr||Qn,yr=e=>{mr=e,e.scope.on()},br=()=>{mr&&mr.scope.off(),mr=null};function wr(e){return 4&e.vnode.shapeFlag}let xr=!1;function _r(e,t,n){v(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:w(t)&&(e.setupState=pn(t)),Ar(e,n)}function Ar(e,t,n){const o=e.type;e.render||(e.render=o.render||r),yr(e),et(),ui(e),tt(),br()}function Sr(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(nt(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}function Tr(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(pn(en(e.exposed)),{get:(t,n)=>n in t?t[n]:n in ai?ai[n](e):void 0,has:(e,t)=>t in e||t in ai}))}function Cr(e,t=!0){return v(e)?e.displayName||e.name:e.name||t&&e.__name}const Er=(e,t)=>function(e,t,n=!1){let o,i;const a=v(e);return a?(o=e,i=r):(o=e.get,i=e.set),new bn(o,i,a||!i,n)}(e,0,xr);function kr(){return function(){const e=vr();return e.setupContext||(e.setupContext=Sr(e))}().attrs}function Pr(e,t,n){const o=arguments.length;return 2===o?w(t)&&!p(t)?Zi(t)?rr(e,null,[t]):rr(e,t):rr(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Zi(n)&&(n=[n]),rr(e,t,n))}const Br=Symbol(""),Mr=()=>eo(Br),Ir="3.2.47",Or="undefined"!=typeof document?document:null,Lr=Or&&Or.createElement("template"),Dr={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const i=t?Or.createElementNS("http://www.w3.org/2000/svg",e):n?Or.createElement(e,{is:n}):Or.createElement(e);return"select"===e&&o&&null!=o.multiple&&i.setAttribute("multiple",o.multiple),i},createText:e=>Or.createTextNode(e),createComment:e=>Or.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Or.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,i,r){const a=n?n.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),i!==r&&(i=i.nextSibling););else{Lr.innerHTML=o?`<svg>${e}</svg>`:e;const i=Lr.content;if(o){const e=i.firstChild;for(;e.firstChild;)i.appendChild(e.firstChild);i.removeChild(e)}t.insertBefore(i,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const zr=/\s*!important$/;function Rr(e,t,n){if(p(n))n.forEach((n=>Rr(e,t,n)));else if(null==n&&(n=""),n=$r(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=jr[t];if(n)return n;let o=P(t);if("filter"!==o&&o in e)return jr[t]=o;o=I(o);for(let i=0;i<Nr.length;i++){const n=Nr[i]+o;if(n in e)return jr[t]=n}return t}(e,t);zr.test(n)?e.setProperty(M(o),n.replace(zr,""),"important"):e[o]=n}}const Nr=["Webkit","Moz","ms"],jr={};const{unit:Fr,unitRatio:qr,unitPrecision:Vr}={unit:"rem",unitRatio:10/320,unitPrecision:5},Hr=(Qr=Fr,Ur=qr,Wr=Vr,e=>e.replace(be,((e,t)=>{if(!t)return e;if(1===Ur)return`${t}${Qr}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Ur,Wr);return 0===n?"0":`${n}${Qr}`})));var Qr,Ur,Wr;const $r=e=>y(e)?Hr(e):e,Xr="http://www.w3.org/1999/xlink";function Yr(e,t,n,o){e.addEventListener(t,n,o)}function Jr(e,t,n,o,i=null){const r=e._vei||(e._vei={}),a=r[t];if(o&&a)a.value=o;else{const[n,s]=function(e){let t;if(Gr.test(e)){let n;for(t={};n=e.match(Gr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):M(e.slice(2)),t]}(t);if(o){const a=r[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,i=o&&o.$nne,{value:r}=n;if(i&&p(r)){const n=ea(e,r);for(let o=0;o<n.length;o++){const r=n[o];xn(r,t,5,r.__wwe?[e]:i(e))}}else xn(ea(e,r),t,5,i&&!r.__wwe?i(e,r,t):[e])};return n.value=e,n.attached=(()=>Kr||(Zr.then((()=>Kr=0)),Kr=Date.now()))(),n}(o,i);Yr(e,n,a,s)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,s),r[t]=void 0)}}const Gr=/(?:Once|Passive|Capture)$/;let Kr=0;const Zr=Promise.resolve();function ea(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const ta=/^on[a-z]/;const na="transition",oa=(e,{slots:t})=>Pr(uo,function(e){const t={};for(const c in e)c in ia||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:i,enterFromClass:r=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:l=r,appearActiveClass:u=a,appearToClass:d=s,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(w(e))return[sa(e.enter),sa(e.leave)];{const t=sa(e);return[t,t]}}(i),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:x,onLeave:_,onLeaveCancelled:A,onBeforeAppear:S=y,onAppear:T=b,onAppearCancelled:C=x}=t,E=(e,t,n)=>{ca(e,t?d:s),ca(e,t?u:a),n&&n()},k=(e,t)=>{e._isLeaving=!1,ca(e,f),ca(e,h),ca(e,p),t&&t()},P=e=>(t,n)=>{const i=e?T:b,a=()=>E(t,e,n);ra(i,[t,a]),ua((()=>{ca(t,e?l:r),la(t,e?d:s),aa(i)||fa(t,o,m,a)}))};return c(t,{onBeforeEnter(e){ra(y,[e]),la(e,r),la(e,a)},onBeforeAppear(e){ra(S,[e]),la(e,l),la(e,u)},onEnter:P(!1),onAppear:P(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>k(e,t);la(e,f),document.body.offsetHeight,la(e,p),ua((()=>{e._isLeaving&&(ca(e,f),la(e,h),aa(_)||fa(e,o,v,n))})),ra(_,[e,n])},onEnterCancelled(e){E(e,!1),ra(x,[e])},onAppearCancelled(e){E(e,!0),ra(C,[e])},onLeaveCancelled(e){k(e),ra(A,[e])}})}(e),t);oa.displayName="Transition";const ia={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};oa.props=c({},co,ia);const ra=(e,t=[])=>{p(e)?e.forEach((e=>e(...t))):e&&e(...t)},aa=e=>!!e&&(p(e)?e.some((e=>e.length>1)):e.length>1);function sa(e){const t=(e=>{const t=y(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function la(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function ca(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function ua(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let da=0;function fa(e,t,n,o){const i=e._endId=++da,r=()=>{i===e._endId&&o()};if(n)return setTimeout(r,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),i=o("transitionDelay"),r=o("transitionDuration"),a=pa(i,r),s=o("animationDelay"),l=o("animationDuration"),c=pa(s,l);let u=null,d=0,f=0;t===na?a>0&&(u=na,d=a,f=r.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(a,c),u=d>0?a>c?na:"animation":null,f=u?u===na?r.length:l.length:0);const p=u===na&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,f),r()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),s+1),e.addEventListener(c,f)}function pa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>ha(t)+ha(e[n]))))}function ha(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const ga=e=>{const t=e.props["onUpdate:modelValue"]||!1;return p(t)?e=>D(t,e):t};function ma(e){e.target.composing=!0}function va(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ya={created(e,{modifiers:{lazy:t,trim:n,number:o}},i){e._assign=ga(i);const r=o||i.props&&"number"===i.props.type;Yr(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),r&&(o=R(o)),e._assign(o)})),n&&Yr(e,"change",(()=>{e.value=e.value.trim()})),t||(Yr(e,"compositionstart",ma),Yr(e,"compositionend",va),Yr(e,"change",va))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:i}},r){if(e._assign=ga(r),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((i||"number"===e.type)&&R(e.value)===t)return}const a=null==t?"":t;e.value!==a&&(e.value=a)}},ba={deep:!0,created(e,t,n){e._assign=ga(n),Yr(e,"change",(()=>{const t=e._modelValue,n=Sa(e),o=e.checked,i=e._assign;if(p(t)){const e=X(t,n),r=-1!==e;if(o&&!r)i(t.concat(n));else if(!o&&r){const n=[...t];n.splice(e,1),i(n)}}else if(g(t)){const e=new Set(t);o?e.add(n):e.delete(n),i(e)}else i(Ta(e,o))}))},mounted:wa,beforeUpdate(e,t,n){e._assign=ga(n),wa(e,t,n)}};function wa(e,{value:t,oldValue:n},o){e._modelValue=t,p(t)?e.checked=X(t,o.props.value)>-1:g(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=$(t,Ta(e,!0)))}const xa={created(e,{value:t},n){e.checked=$(t,n.props.value),e._assign=ga(n),Yr(e,"change",(()=>{e._assign(Sa(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=ga(o),t!==n&&(e.checked=$(t,o.props.value))}},_a={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const i=g(t);Yr(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?R(Sa(e)):Sa(e)));e._assign(e.multiple?i?new Set(t):t:t[0])})),e._assign=ga(o)},mounted(e,{value:t}){Aa(e,t)},beforeUpdate(e,t,n){e._assign=ga(n)},updated(e,{value:t}){Aa(e,t)}};function Aa(e,t){const n=e.multiple;if(!n||p(t)||g(t)){for(let o=0,i=e.options.length;o<i;o++){const i=e.options[o],r=Sa(i);if(n)p(t)?i.selected=X(t,r)>-1:i.selected=t.has(r);else if($(Sa(i),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function Sa(e){return"_value"in e?e._value:e.value}function Ta(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Ca={created(e,t,n){Ea(e,t,n,null,"created")},mounted(e,t,n){Ea(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){Ea(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){Ea(e,t,n,o,"updated")}};function Ea(e,t,n,o,i){const r=function(e,t){switch(e){case"SELECT":return _a;case"TEXTAREA":return ya;default:switch(t){case"checkbox":return ba;case"radio":return xa;default:return ya}}}(e.tagName,n.props&&n.props.type)[i];r&&r(e,t,n,o)}const ka=["ctrl","shift","alt","meta"],Pa={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>ka.some((n=>e[`${n}Key`]&&!t.includes(n)))},Ba=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=Pa[t[e]];if(o&&o(n,t))return}return e(n,...o)},Ma={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ia(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ia(e,!0),o.enter(e)):o.leave(e,(()=>{Ia(e,!1)})):Ia(e,t))},beforeUnmount(e,{value:t}){Ia(e,t)}};function Ia(e,t){e.style.display=t?e._vod:"none"}const Oa=c({patchProp:(e,t,n,o,i=!1,r,a,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const i=t.replace("change:",""),{attrs:r}=o,a=r[i],s=(e.__wxsProps||(e.__wxsProps={}))[i];if(s===a)return;e.__wxsProps[i]=a;const l=o.proxy;In((()=>{n(a,s,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,a);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:i}=e;i&&i.length&&(t=(t||"").split(/\s+/).filter((e=>-1===i.indexOf(e))).join(" "),i.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,i):"style"===t?function(e,t,n){const o=e.style,i=y(n);if(n&&!i){if(t&&!y(t))for(const e in t)null==n[e]&&Rr(o,e,"");for(const e in n)Rr(o,e,n[e])}else{const r=o.display;i?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=r)}const{__wxsStyle:r}=e;if(r)for(const a in r)Rr(o,a,r[a])}(e,n,o):s(t)?l(t)||Jr(e,t,0,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&ta.test(t)&&v(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(ta.test(t)&&y(n))return!1;return t in e}(e,t,o,i))?function(e,t,n,o,i,r,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,i,r),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=W(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(l){}s&&e.removeAttribute(t)}(e,t,o,r,a,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,i){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Xr,t.slice(6,t.length)):e.setAttributeNS(Xr,t,n);else{const o=U(t);null==n||o&&!W(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,i))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Dr);let La;const Da=(...e)=>{const t=(La||(La=Ni(Oa))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(y(e)){return document.querySelector(e)}return e}(e);if(!o)return;const i=t._component;v(i)||i.render||i.template||(i.template=o.innerHTML),o.innerHTML="";const r=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t};const za=["{","}"];const Ra=/^(?:\d)+/,Na=/^(?:\w)+/;const ja=Object.prototype.hasOwnProperty,Fa=(e,t)=>ja.call(e,t),qa=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=za){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let i=0,r="";for(;i<e.length;){let a=e[i++];if(a===t){r&&o.push({type:"text",value:r}),r="";let t="";for(a=e[i++];void 0!==a&&a!==n;)t+=a,a=e[i++];const s=a===n,l=Ra.test(t)?"list":s&&Na.test(t)?"named":"unknown";o.push({value:t,type:l})}else r+=a}return r&&o.push({type:"text",value:r}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const i=Array.isArray(t)?"list":(r=t,null!==r&&"object"==typeof r?"named":"unknown");var r;if("unknown"===i)return n;for(;o<e.length;){const r=e[o];switch(r.type){case"text":n.push(r.value);break;case"list":n.push(t[parseInt(r.value,10)]);break;case"named":"named"===i&&n.push(t[r.value])}o++}return n}(o,t)}};function Va(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const i=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return i||void 0}class Ha{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:i}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=i||qa,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Va(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{Fa(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Va(t,this.messages))&&(o=this.messages[t]):n=t,Fa(o,e)?this.formater.interpolate(o[e],n).join(""):e}}function Qa(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&Mf?Mf():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const i=new Ha({locale:e,fallbackLocale:n,messages:t,watcher:o});let r=(e,t)=>{{let e=!1;r=function(t,n){const o=Pm().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,i))),i.t(t,n)}}return r(e,t)};return{i18n:i,f:(e,t,n)=>i.f(e,t,n),t:(e,t)=>r(e,t),add:(e,t,n=!0)=>i.add(e,t,n),watch:e=>i.watchLocale(e),getLocale:()=>i.getLocale(),setLocale:e=>i.setLocale(e)}}function Ua(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */const Wa="undefined"!=typeof window;const $a=Object.assign;function Xa(e,t){const n={};for(const o in t){const i=t[o];n[o]=Ja(i)?i.map(e):e(i)}return n}const Ya=()=>{},Ja=Array.isArray,Ga=/\/$/;function Ka(e,t,n="/"){let o,i={},r="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(o=t.slice(0,l),r=t.slice(l+1,s>-1?s:t.length),i=e(r)),s>-1&&(o=o||t.slice(0,s),a=t.slice(s,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/");let i,r,a=n.length-1;for(i=0;i<o.length;i++)if(r=o[i],"."!==r){if(".."!==r)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i-(i===o.length?1:0)).join("/")}(null!=o?o:t,n),{fullPath:o+(r&&"?")+r+a,path:o,query:i,hash:a}}function Za(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function es(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ts(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ns(e[n],t[n]))return!1;return!0}function ns(e,t){return Ja(e)?os(e,t):Ja(t)?os(t,e):e===t}function os(e,t){return Ja(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var is,rs,as,ss;function ls(e){if(!e)if(Wa){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ga,"")}(rs=is||(is={})).pop="pop",rs.push="push",(ss=as||(as={})).back="back",ss.forward="forward",ss.unknown="";const cs=/^[^#]+#/;function us(e,t){return e.replace(cs,"#")+t}const ds=()=>({left:window.pageXOffset,top:window.pageYOffset});function fs(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),i="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function ps(e,t){return(history.state?history.state.position-t:-1)+e}const hs=new Map;function gs(e,t){const{pathname:n,search:o,hash:i}=t,r=e.indexOf("#");if(r>-1){let t=i.includes(e.slice(r))?e.slice(r).length:1,n=i.slice(t);return"/"!==n[0]&&(n="/"+n),Za(n,"")}return Za(n,e)+o+i}function ms(e,t,n,o=!1,i=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:i?ds():null}}function vs(e){const{history:t,location:n}=window,o={value:gs(e,n)},i={value:t.state};function r(o,r,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](r,"",l),i.value=r}catch(c){n[a?"replace":"assign"](l)}}return i.value||r(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:i,push:function(e,n){const a=$a({},i.value,t.state,{forward:e,scroll:ds()});r(a.current,a,!0),r(e,$a({},ms(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){r(e,$a({},t.state,ms(i.value.back,e,i.value.forward,!0),n,{position:i.value.position}),!0),o.value=e}}}function ys(e){const t=vs(e=ls(e)),n=function(e,t,n,o){let i=[],r=[],a=null;const s=({state:r})=>{const s=gs(e,location),l=n.value,c=t.value;let u=0;if(r){if(n.value=s,t.value=r,a&&a===l)return void(a=null);u=c?r.position-c.position:0}else o(s);i.forEach((e=>{e(n.value,l,{delta:u,type:is.pop,direction:u?u>0?as.forward:as.back:as.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState($a({},e.state,{scroll:ds()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l),{pauseListeners:function(){a=n.value},listen:function(e){i.push(e);const t=()=>{const t=i.indexOf(e);t>-1&&i.splice(t,1)};return r.push(t),t},destroy:function(){for(const e of r)e();r=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=$a({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:us.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function bs(e){return"string"==typeof e||"symbol"==typeof e}const ws={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},xs=Symbol("");var _s,As;function Ss(e,t){return $a(new Error,{type:e,[xs]:!0},t)}function Ts(e,t){return e instanceof Error&&xs in e&&(null==t||!!(e.type&t))}(As=_s||(_s={}))[As.aborted=4]="aborted",As[As.cancelled=8]="cancelled",As[As.duplicated=16]="duplicated";const Cs={sensitive:!1,strict:!1,start:!0,end:!0},Es=/[.+*?^${}()[\]/\\]/g;function ks(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ps(e,t){let n=0;const o=e.score,i=t.score;for(;n<o.length&&n<i.length;){const e=ks(o[n],i[n]);if(e)return e;n++}if(1===Math.abs(i.length-o.length)){if(Bs(o))return 1;if(Bs(i))return-1}return i.length-o.length}function Bs(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ms={type:0,value:""},Is=/[a-zA-Z0-9_]/;function Os(e,t,n){const o=function(e,t){const n=$a({},Cs,t),o=[];let i=n.start?"^":"";const r=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(i+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(i+="/"),i+=o.value.replace(Es,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;r.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){a+=10;try{new RegExp(`(${d})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+s.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),i+=f,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&(i+="(?:/|$)");const a=new RegExp(i,n.sensitive?"":"i");return{re:a,score:o,keys:r,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",i=r[o-1];n[i.name]=e&&i.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const i of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of i)if(0===e.type)n+=e.value;else if(1===e.type){const{value:r,repeatable:a,optional:s}=e,l=r in t?t[r]:"";if(Ja(l)&&!a)throw new Error(`Provided param "${r}" is an array but it is not repeatable (* or + modifiers)`);const c=Ja(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${r}"`);i.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Ms]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const i=[];let r;function a(){r&&i.push(r),r=[]}let s,l=0,c="",u="";function d(){c&&(0===n?r.push({type:0,value:c}):1===n||2===n||3===n?(r.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function f(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&d(),a()):":"===s?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===s?n=2:Is.test(s)?f():(d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),i}(e.path),n),i=$a(o,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Ls(e,t){const n=[],o=new Map;function i(e,n,o){const s=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:zs(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=js(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push($a({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Os(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),s&&e.name&&!Rs(d)&&r(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)i(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&a(d)}return f?()=>{r(f)}:Ya}function r(e){if(bs(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(r),t.alias.forEach(r))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(r),e.alias.forEach(r))}}function a(e){let t=0;for(;t<n.length&&Ps(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Fs(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Rs(e)&&o.set(e.record.name,e)}return t=js({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>i(e))),{addRoute:i,resolve:function(e,t){let i,r,a,s={};if("name"in e&&e.name){if(i=o.get(e.name),!i)throw Ss(1,{location:e});a=i.record.name,s=$a(Ds(t.params,i.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params&&Ds(e.params,i.keys.map((e=>e.name)))),r=i.stringify(s)}else if("path"in e)r=e.path,i=n.find((e=>e.re.test(r))),i&&(s=i.parse(r),a=i.record.name);else{if(i=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!i)throw Ss(1,{location:e,currentLocation:t});a=i.record.name,s=$a({},t.params,e.params),r=i.stringify(s)}const l=[];let c=i;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:r,params:s,matched:l,meta:Ns(l)}},removeRoute:r,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ds(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function zs(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="boolean"==typeof n?n:n[o];return t}function Rs(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ns(e){return e.reduce(((e,t)=>$a(e,t.meta)),{})}function js(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Fs(e,t){return t.children.some((t=>t===e||Fs(e,t)))}const qs=/#/g,Vs=/&/g,Hs=/\//g,Qs=/=/g,Us=/\?/g,Ws=/\+/g,$s=/%5B/g,Xs=/%5D/g,Ys=/%5E/g,Js=/%60/g,Gs=/%7B/g,Ks=/%7C/g,Zs=/%7D/g,el=/%20/g;function tl(e){return encodeURI(""+e).replace(Ks,"|").replace($s,"[").replace(Xs,"]")}function nl(e){return tl(e).replace(Ws,"%2B").replace(el,"+").replace(qs,"%23").replace(Vs,"%26").replace(Js,"`").replace(Gs,"{").replace(Zs,"}").replace(Ys,"^")}function ol(e){return null==e?"":function(e){return tl(e).replace(qs,"%23").replace(Us,"%3F")}(e).replace(Hs,"%2F")}function il(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function rl(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ws," "),i=e.indexOf("="),r=il(i<0?e:e.slice(0,i)),a=i<0?null:il(e.slice(i+1));if(r in t){let e=t[r];Ja(e)||(e=t[r]=[e]),e.push(a)}else t[r]=a}return t}function al(e){let t="";for(let n in e){const o=e[n];if(n=nl(n).replace(Qs,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ja(o)?o.map((e=>e&&nl(e))):[o&&nl(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function sl(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ja(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const ll=Symbol(""),cl=Symbol(""),ul=Symbol(""),dl=Symbol(""),fl=Symbol("");function pl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}function hl(e,t,n,o,i){const r=o&&(o.enterCallbacks[i]=o.enterCallbacks[i]||[]);return()=>new Promise(((a,s)=>{const l=e=>{var l;!1===e?s(Ss(4,{from:n,to:t})):e instanceof Error?s(e):"string"==typeof(l=e)||l&&"object"==typeof l?s(Ss(2,{from:t,to:e})):(r&&o.enterCallbacks[i]===r&&"function"==typeof e&&r.push(e),a())},c=e.call(o&&o.instances[i],t,n,l);let u=Promise.resolve(c);e.length<3&&(u=u.then(l)),u.catch((e=>s(e)))}))}function gl(e,t,n,o){const i=[];for(const a of e)for(const e in a.components){let s=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(r=s)||"displayName"in r||"props"in r||"__vccOpts"in r){const r=(s.__vccOpts||s)[t];r&&i.push(hl(r,n,o,a,e))}else{let r=s();i.push((()=>r.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const r=(s=i).__esModule||"Module"===s[Symbol.toStringTag]?i.default:i;var s;a.components[e]=r;const l=(r.__vccOpts||r)[t];return l&&hl(l,n,o,a,e)()}))))}}var r;return i}function ml(e){const t=eo(ul),n=eo(dl),o=Er((()=>t.resolve(dn(e.to)))),i=Er((()=>{const{matched:e}=o.value,{length:t}=e,i=e[t-1],r=n.matched;if(!i||!r.length)return-1;const a=r.findIndex(es.bind(null,i));if(a>-1)return a;const s=yl(e[t-2]);return t>1&&yl(i)===s&&r[r.length-1].path!==s?r.findIndex(es.bind(null,e[t-2])):a})),r=Er((()=>i.value>-1&&function(e,t){for(const n in t){const o=t[n],i=e[n];if("string"==typeof o){if(o!==i)return!1}else if(!Ja(i)||i.length!==o.length||o.some(((e,t)=>e!==i[t])))return!1}return!0}(n.params,o.value.params))),a=Er((()=>i.value>-1&&i.value===n.matched.length-1&&ts(n.params,o.value.params)));return{route:o,href:Er((()=>o.value.href)),isActive:r,isExactActive:a,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[dn(e.replace)?"replace":"push"](dn(e.to)).catch(Ya):Promise.resolve()}}}const vl=yo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ml,setup(e,{slots:t}){const n=Wt(ml(e)),{options:o}=eo(ul),i=Er((()=>({[bl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[bl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Pr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}});function yl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const bl=(e,t,n)=>null!=e?e:null!=t?t:n;function wl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const xl=yo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=eo(fl),i=Er((()=>e.route||o.value)),r=eo(cl,0),a=Er((()=>{let e=dn(r);const{matched:t}=i.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=Er((()=>i.value.matched[a.value]));Zn(cl,Er((()=>a.value+1))),Zn(ll,s),Zn(fl,i);const l=sn();return oo((()=>[l.value,s.value,e.name]),(([e,t,n],[o,i,r])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),!e||!t||i&&es(t,i)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=i.value,r=e.name,a=s.value,c=a&&a.components[r];if(!c)return wl(n.default,{Component:c,route:o});const u=a.props[r],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Pr(c,$a({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[r]=null)},ref:l}));return wl(n.default,{Component:f,route:o})||f}}});function _l(e){const t=Ls(e.routes,e),n=e.parseQuery||rl,o=e.stringifyQuery||al,i=e.history,r=pl(),a=pl(),s=pl(),l=ln(ws);let c=ws;Wa&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Xa.bind(null,(e=>""+e)),d=Xa.bind(null,ol),f=Xa.bind(null,il);function p(e,r){if(r=$a({},r||l.value),"string"==typeof e){const o=Ka(n,e,r.path),a=t.resolve({path:o.path},r),s=i.createHref(o.fullPath);return $a(o,a,{params:f(a.params),hash:il(o.hash),redirectedFrom:void 0,href:s})}let a;if("path"in e)a=$a({},e,{path:Ka(n,e.path,r.path).path});else{const t=$a({},e.params);for(const e in t)null==t[e]&&delete t[e];a=$a({},e,{params:d(e.params)}),r.params=d(r.params)}const s=t.resolve(a,r),c=e.hash||"";s.params=u(f(s.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,$a({},e,{hash:(h=c,tl(h).replace(Gs,"{").replace(Zs,"}").replace(Ys,"^")),path:s.path}));var h;const g=i.createHref(p);return $a({fullPath:p,hash:c,query:o===al?sl(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ka(n,e,l.value.path):$a({},e)}function g(e,t){if(c!==e)return Ss(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),$a({query:e.query,hash:e.hash,params:"path"in o?{}:e.params},o)}}function y(e,t){const n=c=p(e),i=l.value,r=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y($a(h(u),{state:"object"==typeof u?$a({},r,u.state):r,force:a,replace:s}),t||n);const d=n;let f;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,i=n.matched.length-1;return o>-1&&o===i&&es(t.matched[o],n.matched[i])&&ts(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,i,n)&&(f=Ss(16,{to:d,from:i}),B(i,i,!0,!1)),(f?Promise.resolve(f):w(d,i)).catch((e=>Ts(e)?Ts(e,2)?e:P(e):k(e,d,i))).then((e=>{if(e){if(Ts(e,2))return y($a({replace:s},h(e.to),{state:"object"==typeof e.to?$a({},r,e.to.state):r,force:a}),t||d)}else e=_(d,i,!0,s,r);return x(d,i,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function w(e,t){let n;const[o,i,s]=function(e,t){const n=[],o=[],i=[],r=Math.max(t.matched.length,e.matched.length);for(let a=0;a<r;a++){const r=t.matched[a];r&&(e.matched.find((e=>es(e,r)))?o.push(r):n.push(r));const s=e.matched[a];s&&(t.matched.find((e=>es(e,s)))||i.push(s))}return[n,o,i]}(e,t);n=gl(o.reverse(),"beforeRouteLeave",e,t);for(const r of o)r.leaveGuards.forEach((o=>{n.push(hl(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),Al(n).then((()=>{n=[];for(const o of r.list())n.push(hl(o,e,t));return n.push(l),Al(n)})).then((()=>{n=gl(i,"beforeRouteUpdate",e,t);for(const o of i)o.updateGuards.forEach((o=>{n.push(hl(o,e,t))}));return n.push(l),Al(n)})).then((()=>{n=[];for(const o of e.matched)if(o.beforeEnter&&!t.matched.includes(o))if(Ja(o.beforeEnter))for(const i of o.beforeEnter)n.push(hl(i,e,t));else n.push(hl(o.beforeEnter,e,t));return n.push(l),Al(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=gl(s,"beforeRouteEnter",e,t),n.push(l),Al(n)))).then((()=>{n=[];for(const o of a.list())n.push(hl(o,e,t));return n.push(l),Al(n)})).catch((e=>Ts(e,8)?e:Promise.reject(e)))}function x(e,t,n){for(const o of s.list())o(e,t,n)}function _(e,t,n,o,r){const a=g(e,t);if(a)return a;const s=t===ws,c=Wa?history.state:{};n&&(o||s?i.replace(e.fullPath,$a({scroll:s&&c&&c.scroll},r)):i.push(e.fullPath,r)),l.value=e,B(e,t,n,s),P()}let A;function S(){A||(A=i.listen(((e,t,n)=>{if(!L.listening)return;const o=p(e),r=v(o);if(r)return void y($a(r,{replace:!0}),o).catch(Ya);c=o;const a=l.value;var s,u;Wa&&(s=ps(a.fullPath,n.delta),u=ds(),hs.set(s,u)),w(o,a).catch((e=>Ts(e,12)?e:Ts(e,2)?(y(e.to,o).then((e=>{Ts(e,20)&&!n.delta&&n.type===is.pop&&i.go(-1,!1)})).catch(Ya),Promise.reject()):(n.delta&&i.go(-n.delta,!1),k(e,o,a)))).then((e=>{(e=e||_(o,a,!1))&&(n.delta&&!Ts(e,8)?i.go(-n.delta,!1):n.type===is.pop&&Ts(e,20)&&i.go(-1,!1)),x(o,a,e)})).catch(Ya)})))}let T,C=pl(),E=pl();function k(e,t,n){P(e);const o=E.list();return o.length&&o.forEach((o=>o(e,t,n))),Promise.reject(e)}function P(e){return T||(T=!e,S(),C.list().forEach((([t,n])=>e?n(e):t())),C.reset()),e}function B(t,n,o,i){const{scrollBehavior:r}=e;if(!Wa||!r)return Promise.resolve();const a=!o&&function(e){const t=hs.get(e);return hs.delete(e),t}(ps(t.fullPath,0))||(i||!o)&&history.state&&history.state.scroll||null;return In().then((()=>r(t,n,a))).then((e=>e&&fs(e))).catch((e=>k(e,t,n)))}const M=e=>i.go(e);let I;const O=new Set,L={currentRoute:l,listening:!0,addRoute:function(e,n){let o,i;return bs(e)?(o=t.getRecordMatcher(e),i=n):i=e,t.addRoute(i,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:m,replace:function(e){return m($a(h(e),{replace:!0}))},go:M,back:()=>M(-1),forward:()=>M(1),beforeEach:r.add,beforeResolve:a.add,afterEach:s.add,onError:E.add,isReady:function(){return T&&l.value!==ws?Promise.resolve():new Promise(((e,t)=>{C.add([e,t])}))},install(e){e.component("RouterLink",vl),e.component("RouterView",xl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>dn(l)}),Wa&&!I&&l.value===ws&&(I=!0,m(i.location).catch((e=>{})));const t={};for(const o in ws)t[o]=Er((()=>l.value[o]));e.provide(ul,this),e.provide(dl,Wt(t)),e.provide(fl,l);const n=e.unmount;O.add(e),e.unmount=function(){O.delete(e),O.size<1&&(c=ws,A&&A(),A=null,l.value=ws,I=!1,T=!1),n()}}};return L}function Al(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function Sl(){return eo(dl)}const Tl=he((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let Cl;function El(e){return Ua(e,ne)?Bl().f(e,function(){const e=Mf(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ne):e}function kl(e,t){if(1===t.length){if(e){const n=e=>y(e)&&Ua(e,ne),o=t[0];let i=[];if(p(e)&&(i=e.filter((e=>n(e[o])))).length)return i;const r=e[t[0]];if(n(r))return e}return}const n=t.shift();return kl(e&&e[n],t)}function Pl(e,t){const n=kl(e,t);if(!n)return!1;const o=t[t.length-1];if(p(n))n.forEach((e=>Pl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>El(e),set(t){e=t}})}return!0}function Bl(){if(!Cl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,Cl=Qa(e),Tl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>Cl.add(e,__uniConfig.locales[e]))),Cl.setLocale(e)}}return Cl}function Ml(e,t,n){return t.reduce(((t,o,i)=>(t[e+o]=n[i],t)),{})}const Il=he((()=>{const e="uni.async.",t=["error"];Bl().add("en",Ml(e,t,["The connection timed out, click the screen to try again."]),!1),Bl().add("es",Ml(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),Bl().add("fr",Ml(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),Bl().add("zh-Hans",Ml(e,t,["连接服务器超时，点击屏幕重试"]),!1),Bl().add("zh-Hant",Ml(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Ol=he((()=>{const e="uni.showActionSheet.",t=["cancel"];Bl().add("en",Ml(e,t,["Cancel"]),!1),Bl().add("es",Ml(e,t,["Cancelar"]),!1),Bl().add("fr",Ml(e,t,["Annuler"]),!1),Bl().add("zh-Hans",Ml(e,t,["取消"]),!1),Bl().add("zh-Hant",Ml(e,t,["取消"]),!1)})),Ll=he((()=>{const e="uni.showToast.",t=["unpaired"];Bl().add("en",Ml(e,t,["Please note showToast must be paired with hideToast"]),!1),Bl().add("es",Ml(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),Bl().add("fr",Ml(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),Bl().add("zh-Hans",Ml(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),Bl().add("zh-Hant",Ml(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),Dl=he((()=>{const e="uni.showLoading.",t=["unpaired"];Bl().add("en",Ml(e,t,["Please note showLoading must be paired with hideLoading"]),!1),Bl().add("es",Ml(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),Bl().add("fr",Ml(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),Bl().add("zh-Hans",Ml(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),Bl().add("zh-Hant",Ml(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),zl=he((()=>{const e="uni.showModal.",t=["cancel","confirm"];Bl().add("en",Ml(e,t,["Cancel","OK"]),!1),Bl().add("es",Ml(e,t,["Cancelar","OK"]),!1),Bl().add("fr",Ml(e,t,["Annuler","OK"]),!1),Bl().add("zh-Hans",Ml(e,t,["取消","确定"]),!1),Bl().add("zh-Hant",Ml(e,t,["取消","確定"]),!1)})),Rl=he((()=>{const e="uni.chooseFile.",t=["notUserActivation"];Bl().add("en",Ml(e,t,["File chooser dialog can only be shown with a user activation"]),!1),Bl().add("es",Ml(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),Bl().add("fr",Ml(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),Bl().add("zh-Hans",Ml(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),Bl().add("zh-Hant",Ml(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Nl=he((()=>{const e="uni.setClipboardData.",t=["success","fail"];Bl().add("en",Ml(e,t,["Content copied","Copy failed, please copy manually"]),!1),Bl().add("es",Ml(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),Bl().add("fr",Ml(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),Bl().add("zh-Hans",Ml(e,t,["内容已复制","复制失败，请手动复制"]),!1),Bl().add("zh-Hant",Ml(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),jl=he((()=>{const e="uni.video.",t=["danmu","volume"];Bl().add("en",Ml(e,t,["Danmu","Volume"]),!1),Bl().add("es",Ml(e,t,["Danmu","Volumen"]),!1),Bl().add("fr",Ml(e,t,["Danmu","Le Volume"]),!1),Bl().add("zh-Hans",Ml(e,t,["弹幕","音量"]),!1),Bl().add("zh-Hant",Ml(e,t,["彈幕","音量"]),!1)})),Fl=he((()=>{const e="uni.chooseLocation.",t=["search","cancel"];Bl().add("en",Ml(e,t,["Find Place","Cancel"]),!1),Bl().add("es",Ml(e,t,["Encontrar","Cancelar"]),!1),Bl().add("fr",Ml(e,t,["Trouve","Annuler"]),!1),Bl().add("zh-Hans",Ml(e,t,["搜索地点","取消"]),!1),Bl().add("zh-Hant",Ml(e,t,["搜索地點","取消"]),!1)}));function ql(e){const t=new Le;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,i=!1){t[i?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,i){t.emit(`${e}.${n}`,o,i)}}}let Vl=1;const Hl=Object.create(null);function Ql(e,t){return e+"."+t}function Ul(e,t,n){t=Ql(e,t),Hl[t]||(Hl[t]=n)}function Wl({id:e,name:t,args:n},o){t=Ql(o,t);const i=t=>{e&&nw.publishHandler("invokeViewApi."+e,t)},r=Hl[t];r?r(n,i):i({})}const $l=c(ql("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:i}=nw,r=n?Vl++:0;n&&o("invokeServiceApi."+r,n,!0),i("invokeServiceApi",{id:r,name:e,args:t})}}),Xl=we(!0);let Yl;function Jl(){Yl&&(clearTimeout(Yl),Yl=null)}let Gl=0,Kl=0;function Zl(e){if(Jl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Gl=t,Kl=n,Yl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function ec(e){if(!Yl)return;if(1!==e.touches.length)return Jl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Gl)>10||Math.abs(n-Kl)>10?Jl():void 0}function tc(e,t){const n=Number(e);return isNaN(n)?t:n}function nc(){const e=__uniConfig.globalStyle||{},t=tc(e.rpxCalcMaxDeviceWidth,960),n=tc(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function oc(){nc(),ve(),window.addEventListener("touchstart",Zl,Xl),window.addEventListener("touchmove",ec,Xl),window.addEventListener("touchend",Jl,Xl),window.addEventListener("touchcancel",Jl,Xl)}function ic(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var rc,ac,sc=["top","left","right","bottom"],lc={};function cc(){return ac="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function uc(){if(ac="string"==typeof ac?ac:cc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(s){}var o=document.createElement("div");i(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),sc.forEach((function(e){a(o,e)})),document.body.appendChild(o),r(),rc=!0}else sc.forEach((function(e){lc[e]=0}));function i(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function r(t){t?e.push(t):e.forEach((function(e){e()}))}function a(e,n){var o=document.createElement("div"),a=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:ac+"(safe-area-inset-"+n+")"};i(o,c),i(a,c),i(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),i(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(s),a.appendChild(l),e.appendChild(o),e.appendChild(a),r((function(){o.scrollTop=a.scrollTop=1e4;var e=o.scrollTop,i=a.scrollTop;function r(){this.scrollTop!==(this===o?e:i)&&(o.scrollTop=a.scrollTop=1e4,e=o.scrollTop,i=a.scrollTop,function(e){fc.length||setTimeout((function(){var e={};fc.forEach((function(t){e[t]=lc[t]})),fc.length=0,pc.forEach((function(t){t(e)}))}),0);fc.push(e)}(n))}o.addEventListener("scroll",r,t),a.addEventListener("scroll",r,t)}));var u=getComputedStyle(o);Object.defineProperty(lc,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function dc(e){return rc||uc(),lc[e]}var fc=[];var pc=[];const hc=ic({get support(){return 0!=("string"==typeof ac?ac:cc()).length},get top(){return dc("top")},get left(){return dc("left")},get right(){return dc("right")},get bottom(){return dc("bottom")},onChange:function(e){cc()&&(rc||uc(),"function"==typeof e&&pc.push(e))},offChange:function(e){var t=pc.indexOf(e);t>=0&&pc.splice(t,1)}}),gc=Ba((()=>{}),["prevent"]),mc=Ba((e=>{}),["stop"]);function vc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function yc(){const e=vc(document.documentElement.style,"--window-top");return e?e+hc.top:0}function bc(){const e=document.documentElement.style,t=yc(),n=vc(e,"--window-bottom"),o=vc(e,"--window-left"),i=vc(e,"--window-right"),r=vc(e,"--top-window-height");return{top:t,bottom:n?n+hc.bottom:0,left:o?o+hc.left:0,right:i?i+hc.right:0,topWindowHeight:r||0}}function wc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function xc(e){return wc(e)}function _c(e){return Symbol(e)}function Ac(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Sc(e,t=!1){if(t)return function(e){if(!Ac(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>Vd(parseFloat(t))+"px"))}(e);if(y(e)){const t=parseInt(e)||0;return Ac(e)?Vd(t):t}return e}const Tc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Cc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",Ec="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",kc="M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z",Pc="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function Bc(e,t="#000",n=27){return rr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[rr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Mc(){{const{$pageInstance:e}=vr();return e&&e.proxy.$page.id}}function Ic(e){const t=re(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function Oc(){const e=rm(),t=e.length;if(t)return e[t-1]}function Lc(){const e=Oc();if(e)return e.$page.meta}function Dc(){const e=Lc();return e?e.id:-1}function zc(){const e=Oc();if(e)return e.$vm}const Rc=["navigationBar","pullToRefresh"];function Nc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);Rc.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:i}=o;return i.titleText&&i.titleImage&&(i.titleText=""),o}function jc(e,t,n){if(y(e))n=t,t=e,e=zc();else if("number"==typeof e){const t=rm().find((t=>t.$page.id===e));e=t?t.$vm:zc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Fc(e){e.preventDefault()}let qc,Vc=0;function Hc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,i=!1,r=!0;const a=()=>{function a(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,r=o>0&&e>t&&o+t+n>=e,a=Math.abs(e-Vc)>n;return!r||i&&!a?(!r&&i&&(i=!1),!1):(Vc=e,i=!0,!0)})())return t&&t(),r=!1,setTimeout((function(){r=!0}),350),!0}e&&e(window.pageYOffset),t&&r&&(a()||(qc=setTimeout(a,300))),o=!1};return function(){clearTimeout(qc),o||requestAnimationFrame(a),o=!0}}function Qc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Qc(e,t.slice(2));const n=t.split("/"),o=n.length;let i=0;for(;i<o&&".."===n[i];i++);n.splice(0,i),t=n.join("/");const r=e.length>0?e.split("/"):[];return r.splice(r.length-i-1,i+1),de(r.concat(n).join("/"))}function Uc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Wc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(se(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&se(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Jc(this.$el.querySelector(e));return t?$c(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Jc(n[o]);e&&t.push($c(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||y(e))return t;for(const n in e){const o=e[n],i=n.startsWith("--")?n:M(n);(y(o)||"number"==typeof o)&&(t+=`${i}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(y(e)&&(e=H(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];v(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&nw.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function $c(e,t=!0){if(t&&e&&(e=ae(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Wc(e)),e.$el.__wxsComponentDescriptor}function Xc(e,t){return $c(e,t)}function Yc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Xc(n.proxy,!1)}));const i=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ae(t);if(!o)return!1;const i=o.$.type;return!(!i.$wxs&&!i.$renderjs)&&o}(t,n,o);if(i)return[e,Xc(i,!1)]}}function Jc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Gc(e,t=!1){const{type:n,timeStamp:o,target:i,currentTarget:r}=e;let a,s;a=xe(t?i:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(i)),s=xe(r);const l={type:n,timeStamp:o,target:a,detail:{},currentTarget:s};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Kc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Zc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:i,pageX:r,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:i,pageX:r,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const eu=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const i=0!==o.tagName.indexOf("UNI-");if(i)return Yc(e,t,n,!1)||[e];const r=Gc(e,i);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,i=yc();e.detail={x:n,y:o-i},e.touches=e.changedTouches=[Kc(t,i)]}(r,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=yc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Kc(t,n)]}(r,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=yc();r.touches=Zc(e.touches,t),r.changedTouches=Zc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(r,t,{get:()=>e[t]})}))}return Yc(r,t,n)||[r]},createNativeEvent:Gc},Symbol.toStringTag,{value:"Module"});function tu(e){!function(e){const t=e.globalProperties;c(t,eu),t.$gcd=Xc}(e._context.config)}let nu=1;function ou(e){return(e||Dc())+".invokeViewApi"}const iu=c(ql("view"),{invokeOnCallback:(e,t)=>ow.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:i,publishHandler:r}=ow,a=o?nu++:0;o&&i("invokeViewApi."+a,o,!0),r(ou(n),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:i,unsubscribe:r,publishHandler:a}=ow,s=nu++,l="invokeViewApi."+s;return i(l,n),a(ou(o),{id:s,name:e,args:t},o),()=>{r(l)}}});function ru(e){jc(Oc(),"onResize",e),ow.invokeOnCallback("onWindowResize",e)}function au(e){const t=Oc();jc(Pm(),"onShow",e),jc(t,"onShow")}function su(){jc(Pm(),"onHide"),jc(Oc(),"onHide")}const lu=["onPageScroll","onReachBottom"];function cu(){lu.forEach((e=>ow.subscribe(e,function(e){return(t,n)=>{jc(parseInt(n),e,t)}}(e))))}function uu(){!function(){const{on:e}=ow;e("onResize",ru),e("onAppEnterForeground",au),e("onAppEnterBackground",su)}(),cu()}function du(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ee(this.$page.id)),e.eventChannel}}function fu(e){e._context.config.globalProperties.getOpenerEventChannel=du}function pu(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function hu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${Vd(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function gu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,i=t.actions.length;function r(){const t=o[n],a=t.option.transition,s=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],i=["width","height","left","right","top","bottom"],r=e.animates,a=e.option,s=a.transition,l={},c=[];return r.forEach((e=>{let r=e.type,a=[...e.args];if(t.concat(n).includes(r))r.startsWith("rotate")||r.startsWith("skew")?a=a.map((e=>parseFloat(e)+"deg")):r.startsWith("translate")&&(a=a.map(hu)),n.indexOf(r)>=0&&(a.length=1),c.push(`${r}(${a.join(",")})`);else if(o.concat(i).includes(a[0])){r=a[0];const e=a[1];l[r]=i.includes(r)?hu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),n+=1,n<i&&setTimeout(r,a.duration+a.delay)}setTimeout((()=>{r()}),0)}const mu={props:["animation"],watch:{animation:{deep:!0,handler(){gu(this)}}},mounted(){gu(this)}},vu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(mu),yu(e)},yu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},yo(e));function bu(e){return e.__wwe=!0,e}function wu(e,t){return(n,o,i)=>{e.value&&t(n,function(e,t,n,o){let i;return i=xe(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:i,currentTarget:i,detail:o}}(n,o,e.value,i||{}))}}const xu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function _u(e){const t=sn(!1);let n,o,i=!1;function r(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function a(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),i=!0,n=setTimeout((()=>{t.value=!0,i||r()}),parseInt(e.hoverStartTime)))}function s(){i=!1,t.value&&r()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:bu((function(e){e.touches.length>1||a(e)})),onMousedown:bu((function(e){i||(a(e),window.addEventListener("mouseup",l))})),onTouchend:bu((function(){s()})),onMouseup:bu((function(){i&&l()})),onTouchcancel:bu((function(){i=!1,t.value=!1,clearTimeout(n)}))}}}function Au(e,t){return y(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const Su=_c("uf"),Tu={for:{type:String,default:""}},Cu=_c("ul");const Eu=vu({name:"Label",props:Tu,setup(e,{slots:t}){const n=sn(null),o=Mc(),i=function(){const e=[];return Zn(Cu,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),r=Er((()=>e.for||t.default&&t.default.length)),a=bu((t=>{const n=t.target;let r=/^uni-(checkbox|radio|switch)-/.test(n.className);r||(r=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(n.tagName)),r||(e.for?nw.emit("uni-label-click-"+o+"-"+e.for,t,!0):i.length&&i[0](t,!0))}));return()=>rr("uni-label",{ref:n,class:{"uni-label-pointer":r},onClick:a},[t.default&&t.default()],10,["onClick"])}});function ku(e,t){Pu(e.id,t),oo((()=>e.id),((e,n)=>{Bu(n,t,!0),Pu(e,t,!0)})),Ho((()=>{Bu(e.id,t)}))}function Pu(e,t,n){const o=Mc();n&&!e||S(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&nw.on(`uni-${i}-${o}-${e}`,t[i]):0===i.indexOf("uni-")?nw.on(i,t[i]):e&&nw.on(`uni-${i}-${o}-${e}`,t[i])}))}function Bu(e,t,n){const o=Mc();n&&!e||S(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&nw.off(`uni-${i}-${o}-${e}`,t[i]):0===i.indexOf("uni-")?nw.off(i,t[i]):e&&nw.off(`uni-${i}-${o}-${e}`,t[i])}))}const Mu=vu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null),o=eo(Su,!1),{hovering:i,binding:r}=_u(e);Bl();const a=bu(((t,i)=>{if(e.disabled)return t.stopImmediatePropagation();i&&n.value.click();const r=e.formType;if(r){if(!o)return;"submit"===r?o.submit(t):"reset"===r&&o.reset(t)}else;})),s=eo(Cu,!1);return s&&(s.addHandler(a),Vo((()=>{s.removeHandler(a)}))),ku(e,{"label-click":a}),()=>{const o=e.hoverClass,s=Au(e,"disabled"),l=Au(e,"loading"),c=Au(e,"plain"),u=o&&"none"!==o;return rr("uni-button",fr({ref:n,onClick:a,id:e.id,class:u&&i.value?o:""},u&&r,s,l,c),[t.default&&t.default()],16,["onClick","id"])}}});function Iu(e){return e.$el}function Ou(e){const{base:t}=__uniConfig.router;return 0===de(e).indexOf(t)?de(e):t+e}function Lu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Ou(e.slice(1));e="https:"+e}if(oe.test(e)||ie.test(e)||0===e.indexOf("blob:"))return e;const o=rm();return o.length?Ou(Qc(o[o.length-1].$page.route,e).slice(1)):e}const Du=navigator.userAgent,zu=/android/i.test(Du),Ru=/iphone|ipad|ipod/i.test(Du),Nu=Du.match(/Windows NT ([\d|\d.\d]*)/i),ju=/Macintosh|Mac/i.test(Du),Fu=/Linux|X11/i.test(Du),qu=ju&&navigator.maxTouchPoints>0;function Vu(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Hu(e){return e&&90===Math.abs(window.orientation)}function Qu(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Uu(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Wu(e,t,n,o){ow.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function $u(e,t){const n={},{top:o,topWindowHeight:i}=bc();if(t.node){const t=e.tagName.split("-")[1];t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=ye(e)),t.rect||t.size){const r=e.getBoundingClientRect();t.rect&&(n.left=r.left,n.right=r.right,n.top=r.top-o-i,n.bottom=r.bottom-o-i),t.size&&(n.width=r.width,n.height=r.height)}if(p(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(p(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Xu(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function Yu(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:i,fields:r})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(r)):o.push(function(e,t,n,o,i){const r=function(e,t){return e?e.$el:t.$el}(t,e),a=r.parentElement;if(!a)return o?null:[];const{nodeType:s}=r,l=3===s||8===s;if(o){const e=l?a.querySelector(n):Xu(r,n)?r:r.querySelector(n);return e?$u(e,i):null}{let e=[];const t=(l?a:r).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push($u(t,i))})),!l&&Xu(r,n)&&e.unshift($u(r,i)),e}}(e,t,n,i,r))})),n(o)}const Ju=["original","compressed"],Gu=["album","camera"],Ku=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Zu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function ed(e,t){return!p(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function td(e){return function(){try{return e.apply(e,arguments)}catch(t){}}}let nd=1;const od={};function id(e,t,n,o=!1){return od[e]={name:t,keepAlive:o,callback:n},e}function rd(e,t,n){if("number"==typeof e){const o=od[e];if(o)return o.keepAlive||delete od[e],o.callback(t,n)}return t}function ad(e){for(const t in od)if(od[t].name===e)return!0;return!1}const sd="success",ld="fail",cd="complete";function ud(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:i,fail:r,complete:a}=function(e){const t={};for(const n in e){const o=e[n];v(o)&&(t[n]=td(o),delete e[n])}return t}(t),s=v(i),l=v(r),c=v(a),u=nd++;return id(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),v(n)&&n(u),u.errMsg===e+":ok"?(v(o)&&o(u,t),s&&i(u)):l&&r(u),c&&a(u)})),u}const dd="success",fd="fail",pd="complete",hd={},gd={};function md(e,t){return function(n){return e(n,t)||n}}function vd(e,t,n){let o=!1;for(let i=0;i<e.length;i++){const r=e[i];if(o)o=Promise.resolve(md(r,n));else{const e=r(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function yd(e,t={}){return[dd,fd,pd].forEach((n=>{const o=e[n];if(!p(o))return;const i=t[n];t[n]=function(e){vd(o,e,t).then((e=>v(i)&&i(e)||e))}})),t}function bd(e,t){const n=[];p(hd.returnValue)&&n.push(...hd.returnValue);const o=gd[e];return o&&p(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function wd(e){const t=Object.create(null);Object.keys(hd).forEach((e=>{"returnValue"!==e&&(t[e]=hd[e].slice())}));const n=gd[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function xd(e,t,n,o){const i=wd(e);if(i&&Object.keys(i).length){if(p(i.invoke)){return vd(i.invoke,n).then((n=>t(yd(wd(e),n),...o)))}return t(yd(i,n),...o)}return t(n,...o)}function _d(e,t){return(n={},...o)=>function(e){return!(!S(e)||![sd,ld,cd].find((t=>v(e[t]))))}(n)?bd(e,xd(e,t,n,o)):bd(e,new Promise(((i,r)=>{xd(e,t,c(n,{success:i,fail:r}),o)})))}function Ad(e,t,n,o={}){const i=t+":fail"+(n?" "+n:"");return delete o.errCode,rd(e,"undefined"!=typeof UniError?void 0!==o.errCode?new UniError(t,o.errCode,i):new UniError(i,o):c({errMsg:i},o))}function Sd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(y(e))return e}const i=function(e,t){const n=e[0];if(!t||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,i=Object.keys(o);for(let r=0;r<i.length;r++){const t=i[r],a=o[t];if(v(a)){const o=a(e[0][t],n);if(y(o))return o}else f(n,t)||(n[t]=a)}}(t,o);if(i)return i}function Td(e){if(!v(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function Cd(e,t,n){return o=>{Td(o);const i=Sd(0,[o],0,n);if(i)throw new Error(i);const r=!ad(e);!function(e,t){id(nd++,e,t,!0)}(e,o),r&&(!function(e){ow.on("api."+e,(t=>{for(const n in od){const o=od[n];o.name===e&&o.callback(t)}}))}(e),t())}}function Ed(e,t,n){return o=>{Td(o);const i=Sd(0,[o],0,n);if(i)throw new Error(i);!function(e,t){for(const n in od){const o=od[n];o.callback===t&&o.name===e&&delete od[n]}}(e=e.replace("off","on"),o);ad(e)||(!function(e){ow.off("api."+e)}(e),t())}}function kd(e,t,n,o){return n=>{const i=ud(e,n,o),r=Sd(0,[n],0,o);return r?Ad(i,e,r):t(n,{resolve:t=>function(e,t,n){return rd(e,c(n||{},{errMsg:t+":ok"}))}(i,e,t),reject:(t,n)=>Ad(i,e,function(e){return!e||y(e)?e:e.stack?e.message:e}(t),n)})}}function Pd(e,t,n){return Cd(e,t,n)}function Bd(e,t,n){return Ed(e,t,n)}function Md(e,t,n,o){return _d(e,kd(e,t,0,o))}function Id(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Sd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Od(e,t,n,o){return _d(e,function(e,t,n,o){return kd(e,t,0,o)}(e,t,0,o))}let Ld=!1,Dd=0,zd=0,Rd=960,Nd=375,jd=750;function Fd(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=Vu(),t=Uu(Qu(e,Hu(e)));return{platform:Ru?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Dd=n,zd=t,Ld="ios"===e}function qd(e,t){const n=Number(e);return isNaN(n)?t:n}const Vd=Id(0,((e,t)=>{if(0===Dd&&(Fd(),function(){const e=__uniConfig.globalStyle||{};Rd=qd(e.rpxCalcMaxDeviceWidth,960),Nd=qd(e.rpxCalcBaseDeviceWidth,375),jd=qd(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Dd;n=e===jd||n<=Rd?n:Nd;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==zd&&Ld?.5:1),e<0?-o:o}));function Hd(e,t){Object.keys(t).forEach((n=>{v(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):p(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}const Qd=Id(0,((e,t)=>{y(e)&&S(t)?Hd(gd[e]||(gd[e]={}),t):S(e)&&Hd(hd,e)})),Ud=new Le,Wd=Id(0,((e,...t)=>{Ud.emit(e,...t)})),$d=[.5,.8,1,1.25,1.5,2];class Xd{constructor(e,t){this.id=e,this.pageId=t}play(){Wu(this.id,this.pageId,"play")}pause(){Wu(this.id,this.pageId,"pause")}stop(){Wu(this.id,this.pageId,"stop")}seek(e){Wu(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Wu(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~$d.indexOf(e)||(e=1),Wu(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Wu(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Wu(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Wu(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Wu(this.id,this.pageId,"hideStatusBar")}}const Yd=Id(0,((e,t)=>new Xd(e,Ic(t||zc())))),Jd=(e,t,n,o)=>{!function(e,t,n,o,i){ow.invokeViewMethod("map."+e,{type:n,data:o},t,i)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};function Gd(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const Kd=Gd("width"),Zd=Gd("height"),ef={PNG:"png",JPG:"jpg",JPEG:"jpg"},tf={formatArgs:{x:Gd("x",0),y:Gd("y",0),width:Kd,height:Zd,destWidth:Gd("destWidth"),destHeight:Gd("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=ef[e];n||(n=ef.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function nf(e,t,n,o,i){ow.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{i&&i(e)}))}var of=["scale","rotate","translate","setTransform","transform"],rf=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],af=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const sf={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function lf(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(f(sf,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(sf[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),i=parseInt(t[1].slice(4,6),16);let r=parseInt(t[1].slice(6,8),16);return r=r>=0?r:255,[e,o,i,r]}return[0,0,0,255]}class cf{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,lf(t)])}}class uf{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class df{constructor(e){this.width=e}}class ff{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],nf(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new cf("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new cf("radial",[e,t,n])}createPattern(e,t){if(void 0===t);else if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new uf(e,t)}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new df(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,i,r){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,i,r]}),this.subpath.push([i,r])}arc(e,t,n,o,i,r=!1){this.path.push({method:"arc",data:[e,t,n,o,i,r]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,i){this.path.push({method:"arcTo",data:[e,t,n,o,i]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),i=parseFloat(n[3]),r=n[7],a=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(a.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(a.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(a.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&s()})),1===o.length&&s(),o=a.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=i,this.state.fontFamily=r,this.actions.push({method:"setFont",data:[`${o} ${i}px ${r}`]})}function s(){a.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const pf=he((()=>{[...of,...rf].forEach((function(e){ff.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,i){var r=[t.toString(),n,o];"number"==typeof i&&r.push(i),this.actions.push({method:e,data:r})};case"drawImage":return function(t,n,o,i,r,a,s,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(a=n,s=o,l=i,c=r,n=void 0,o=void 0,i=void 0,r=void 0),u=d(n)&&d(o)&&d(i)&&d(r)?[t,a,s,l,c,n,o,i,r]:d(l)&&d(c)?[t,a,s,l,c]:[t,a,s],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),af.forEach((function(e){ff.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",lf(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,i){i=lf(i),this.actions.push({method:e,data:[t,n,o,i]}),this.state.shadowBlur=o,this.state.shadowColor=i,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),hf=Id(0,((e,t)=>{if(pf(),t)return new ff(e,Ic(t));const n=Ic(zc());if(n)return new ff(e,n);ow.emit("onError","createCanvasContext:fail")})),gf=Od("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:i,destHeight:r,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Ic(zc());if(!d)return void u();nf(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:i,destHeight:r,fileType:s,quality:l,dirname:`${Ap}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,tf),mf={thresholds:[0],initialRatio:0,observeAll:!1},vf=["top","right","bottom","left"];let yf=1;function bf(e={}){return vf.map((t=>`${Number(e[t])||0}px`)).join(" ")}class wf{constructor(e,t){this._pageId=Ic(e),this._component=e,this._options=c({},mf,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=bf(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=bf(e),this}observe(e,t){v(t)&&(this._options.selector=e,this._reqId=yf++,function({reqId:e,component:t,options:n,callback:o},i){const r=Iu(t);(r.__io||(r.__io={}))[e]=function(e,t,n){!function(){if("object"!=typeof window)return;if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return void("isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}}));function e(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(t){return null}}var t=function(t){for(var n=window.document,o=e(n);o;)o=e(n=o.ownerDocument);return n}(),n=[],o=null,i=null;function r(e){this.time=e.time,this.target=e.target,this.rootBounds=h(e.rootBounds),this.boundingClientRect=h(e.boundingClientRect),this.intersectionRect=h(e.intersectionRect||p()),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,i=o.width*o.height;this.intersectionRatio=n?Number((i/n).toFixed(4)):this.isIntersecting?1:0}function a(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=l(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(){return window.performance&&performance.now&&performance.now()}function l(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}function c(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function u(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function d(e,t){var n=Math.max(e.top,t.top),o=Math.min(e.bottom,t.bottom),i=Math.max(e.left,t.left),r=Math.min(e.right,t.right),a=r-i,s=o-n;return a>=0&&s>=0&&{top:n,bottom:o,left:i,right:r,width:a,height:s}||null}function f(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):p()}function p(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function h(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function g(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function m(e,t){for(var n=t;n;){if(n==e)return!0;n=v(n)}return!1}function v(n){var o=n.parentNode;return 9==n.nodeType&&n!=t?e(n):(o&&o.assignedSlot&&(o=o.assignedSlot.parentNode),o&&11==o.nodeType&&o.host?o.host:o)}function y(e){return e&&9===e.nodeType}a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return o||(o=function(e,t){i=e&&t?g(e,t):p(),n.forEach((function(e){e._checkForIntersections()}))}),o},a._resetCrossOriginUpdater=function(){o=null,i=null},a.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},a.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},a.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},a.prototype._monitorIntersections=function(n){var o=n.defaultView;if(o&&-1==this._monitoringDocuments.indexOf(n)){var i=this._checkForIntersections,r=null,a=null;this.POLL_INTERVAL?r=o.setInterval(i,this.POLL_INTERVAL):(c(o,"resize",i,!0),c(n,"scroll",i,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in o&&(a=new o.MutationObserver(i)).observe(n,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(n),this._monitoringUnsubscribes.push((function(){var e=n.defaultView;e&&(r&&e.clearInterval(r),u(e,"resize",i,!0)),u(n,"scroll",i,!0),a&&a.disconnect()}));var s=this.root&&(this.root.ownerDocument||this.root)||t;if(n!=s){var l=e(n);l&&this._monitorIntersections(l.ownerDocument)}}},a.prototype._unmonitorIntersections=function(n){var o=this._monitoringDocuments.indexOf(n);if(-1!=o){var i=this.root&&(this.root.ownerDocument||this.root)||t;if(!this._observationTargets.some((function(t){var o=t.element.ownerDocument;if(o==n)return!0;for(;o&&o!=i;){var r=e(o);if((o=r&&r.ownerDocument)==n)return!0}return!1}))){var r=this._monitoringUnsubscribes[o];if(this._monitoringDocuments.splice(o,1),this._monitoringUnsubscribes.splice(o,1),r(),n!=i){var a=e(n);a&&this._unmonitorIntersections(a.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},a.prototype._checkForIntersections=function(){if(this.root||!o||i){var e=this._rootIsInDom(),t=e?this._getRootRect():p();this._observationTargets.forEach((function(n){var i=n.element,a=f(i),l=this._rootContainsTarget(i),c=n.entry,u=e&&l&&this._computeTargetAndRootIntersection(i,a,t),d=null;this._rootContainsTarget(i)?o&&!this.root||(d=t):d=p();var h=n.entry=new r({time:s(),target:i,boundingClientRect:a,rootBounds:d,intersectionRect:u});c?e&&l?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(e,n,r){if("none"!=window.getComputedStyle(e).display){for(var a=n,s=v(e),l=!1;!l&&s;){var c=null,u=1==s.nodeType?window.getComputedStyle(s):{};if("none"==u.display)return null;if(s==this.root||9==s.nodeType)if(l=!0,s==this.root||s==t)o&&!this.root?!i||0==i.width&&0==i.height?(s=null,c=null,a=null):c=i:c=r;else{var p=v(s),h=p&&f(p),m=p&&this._computeTargetAndRootIntersection(p,h,r);h&&m?(s=p,c=g(h,m)):(s=null,a=null)}else{var y=s.ownerDocument;s!=y.body&&s!=y.documentElement&&"visible"!=u.overflow&&(c=f(s))}if(c&&(a=d(c,a)),!a)break;s=s&&v(s)}return a}},a.prototype._getRootRect=function(){var e;if(this.root&&!y(this.root))e=f(this.root);else{var n=y(this.root)?this.root:t,o=n.documentElement,i=n.body;e={top:0,left:0,right:o.clientWidth||i.clientWidth,width:o.clientWidth||i.clientWidth,bottom:o.clientHeight||i.clientHeight,height:o.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(e)},a.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},a.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var i=0;i<this.thresholds.length;i++){var r=this.thresholds[i];if(r==n||r==o||r<n!=r<o)return!0}},a.prototype._rootIsInDom=function(){return!this.root||m(t,this.root)},a.prototype._rootContainsTarget=function(e){var n=this.root&&(this.root.ownerDocument||this.root)||t;return m(n,e)&&(!this.root||n==e.ownerDocument)},a.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},a.prototype._unregisterInstance=function(){var e=n.indexOf(this);-1!=e&&n.splice(e,1)},window.IntersectionObserver=a,window.IntersectionObserverEntry=r}();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,i=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:_p(e),intersectionRect:xp(e.intersectionRect),boundingClientRect:xp(e.boundingClientRect),relativeRect:xp(e.rootBounds),time:Date.now(),dataset:ye(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){i.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)i.observe(n[e])}else{i.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n&&i.observe(n)}return i}(r,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=Iu(t),i=o.__io&&o.__io[e];i&&(i.disconnect(),delete o.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const xf=Id(0,((e,t)=>((e=re(e))&&!Ic(e)&&(t=e,e=null),new wf(e||zc(),t))));let _f=0,Af={};const Sf={canvas:ff,map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){Jd(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){Jd(this.id,this.pageId,"moveToLocation",e)}getScale(e){Jd(this.id,this.pageId,"getScale",e)}getRegion(e){Jd(this.id,this.pageId,"getRegion",e)}includePoints(e){Jd(this.id,this.pageId,"includePoints",e)}translateMarker(e){Jd(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){Jd(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){Jd(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){Jd(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){Jd(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){Jd(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){Jd(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){Jd(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){Jd(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){Jd(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){Jd(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){Jd(this.id,this.pageId,"openMapApp",e)}on(e,t){Jd(this.id,this.pageId,"on",{name:e,callback:t})}},video:Xd,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const i={options:o},r=o&&("success"in o||"fail"in o||"complete"in o);if(r){const e=String(_f++);i.callbackId=e,Af[e]=o}ow.invokeViewMethod(`editor.${e}`,{type:n,data:i},t,(({callbackId:e,data:t})=>{r&&(ge(Af[e],t),delete Af[e])}))}(this.id,this.pageId,e,t)}}};function Tf(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,i=Sf[n];e.context=new i(t,o),delete e.contextInfo}}class Cf{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class Ef{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return Yu(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{p(e)?e.forEach(Tf):Tf(e);const o=n[t];v(o)&&o.call(this,e)})),v(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=re(e),this}select(e){return this._nodesRef=new Cf(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Cf(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Cf(this,null,"",!0)}_push(e,t,n,o,i){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(i)}}const kf=Id(0,(e=>((e=re(e))&&!Ic(e)&&(e=null),new Ef(e||zc())))),Pf=Pd("onWindowResize",(()=>{})),Bf=Bd("offWindowResize",(()=>{})),Mf=Id(0,(()=>{const e=Pm();return e&&e.$vm?e.$vm.$locale:Bl().getLocale()})),If={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const Of=Id(0,(()=>c({},Pp)));let Lf,Df,zf;const Rf=[];const Nf=Od("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{var e,o;void 0===zf&&(zf=!1,Lf="",Df="uniPush is not enabled"),Rf.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==Lf&&(e=Lf,o=Df,Rf.forEach((t=>{t(e,o)})),Rf.length=0)}))})),jf=e=>{},Ff=e=>{},qf={formatArgs:{showToast:!0},beforeInvoke(){Nl()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=Bl(),o=n("uni.setClipboardData.success");o&&pb({title:o,icon:"success",mask:!1})}},Vf=(Boolean,["wgs84","gcj02"]),Hf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===Vf.indexOf(e)?t.type=Vf[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},Qf=(Boolean,{formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=ed(e,Ju)},sourceType(e,t){t.sourceType=ed(e,Gu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}}),Uf={formatArgs:{sourceType(e,t){t.sourceType=ed(e,Gu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Wf=(Boolean,["all","image","video"]),$f={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=ed(e,Gu)},type(e,t){t.type=Zu(e,Wf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Xf={formatArgs:{urls(e,t){t.urls=e.map((e=>y(e)&&e?Lu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:y(e)&&e&&(t.current=Lu(e))}}},Yf="json",Jf=["text","arraybuffer"],Gf=encodeURIComponent;ArrayBuffer,Boolean;const Kf={formatArgs:{method(e,t){t.method=Zu((e||"").toUpperCase(),Ku)},data(e,t){t.data=e||""},url(e,t){t.method===Ku[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let i=n[1]||"";e=n[0];const r=i.split("&").filter((e=>e)),a={};r.forEach((e=>{const t=e.split("=");a[t[0]]=t[1]}));for(const s in t)if(f(t,s)){let e=t[s];null==e?e="":S(e)&&(e=JSON.stringify(e)),a[Gf(s)]=Gf(e)}return i=Object.keys(a).map((e=>`${e}=${a[e]}`)).join("&"),e+(i?"?"+i:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Ku[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Yf).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Jf.indexOf(t.responseType)&&(t.responseType="text")}}},Zf={formatArgs:{header(e,t){t.header=e||{}}}},ep={formatArgs:{filePath(e,t){e&&(t.filePath=Lu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},tp={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=Zu((e||"").toUpperCase(),Ku)},protocols(e,t){y(e)&&(t.protocols=[e])}}};const np={url:{type:String,required:!0}},op=(lp(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),lp(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),dp("navigateTo")),ip=dp("redirectTo"),rp=dp("reLaunch"),ap=dp("switchTab"),sp={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(rm().length-1,e)}}};function lp(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let cp;function up(){cp=""}function dp(e){return{formatArgs:{url:fp(e)},beforeAll:up}}function fp(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=rm();return n.length&&(t=n[n.length-1].$page.route),Qc(t,e)}(t)).split("?")[0],i=Uc(o,!0);if(!i)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(i.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!i.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!i.meta.isTabBar||"appLaunch"===n.openType||(t=o),i.meta.isEntry&&(t=t.replace(i.alias,"/")),n.url=function(e){if(!y(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),i=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(i))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(cp===t&&"appLaunch"!==n.openType)return`${cp} locked`;__uniConfig.ready&&(cp=t)}else if(i.meta.isTabBar){const e=rm(),t=i.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const pp={formatArgs:{duration:300}},hp={formatArgs:{itemColor:"#000"}},gp=(Boolean,{formatArgs:{title:"",mask:!1}}),mp=(Boolean,{beforeInvoke(){zl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!f(t,"cancelText")){const{t:e}=Bl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!f(t,"confirmText")){const{t:e}=Bl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),vp=["success","loading","none","error"],yp=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Zu(e,vp)},image(e,t){t.image=e?Lu(e):""},duration:1500,mask:!1}}),bp={beforeInvoke(){const e=Lc();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},wp={beforeInvoke:bp.beforeInvoke,formatArgs:c({pagePath(e,t){e&&(t.pagePath=fe(e))}},bp.formatArgs)};function xp(e){const{bottom:t,height:n,left:o,right:i,top:r,width:a}=e||{};return{bottom:t,height:n,left:o,right:i,top:r,width:a}}function _p(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:i,width:r}}=e;return 0!==t?t:i===n?r/o:i/n}const Ap="",Sp={};function Tp(e,t){const n=Sp[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",i=atob(t[1]);let r=i.length;const a=new Uint8Array(r);for(;r--;)a[r]=i.charCodeAt(r);return Cp(a,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Cp(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const i=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],i,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||i}}return n}function Ep(e){for(const n in Sp)if(f(Sp,n)){if(Sp[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Sp[t]=e,t}function kp(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete Sp[e]}const Pp=pu(),Bp=pu();const Mp=vu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=sn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),i=function(e,t,n){const o=Wt({width:-1,height:-1});return oo((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){Eo(o),jo((()=>{t.initial&&In(n);const i=e.value;i.offsetParent!==i.parentElement&&(i.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,i,o),()=>rr("uni-resize-sensor",{ref:n,onAnimationstartOnce:i},[rr("div",{onScroll:i},[rr("div",null,null)],40,["onScroll"]),rr("div",{onScroll:i},[rr("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const Ip=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function Op(e,t=!0){const n=t?Ip:1;e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").__hidpi__=t}let Lp=!1;function Dp(){if(Lp)return;Lp=!0;const e={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},t=CanvasRenderingContext2D.prototype;var n;t.drawImageByCanvas=(n=t.drawImage,function(e,t,o,i,r,a,s,l,c,u){if(!this.__hidpi__)return n.apply(this,arguments);t*=Ip,o*=Ip,i*=Ip,r*=Ip,a*=Ip,s*=Ip,l=u?l*Ip:l,c=u?c*Ip:c,n.call(this,e,t,o,i,r,a,s,l,c)}),1!==Ip&&(!function(e,t){for(const n in e)f(e,n)&&t(e[n],n)}(e,(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);let n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*Ip}));else if(Array.isArray(e))for(let t=0;t<e.length;t++)n[e[t]]*=Ip;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=Ip,e.apply(this,arguments),this.lineWidth/=Ip}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);const t=Array.prototype.slice.call(arguments);t[1]*=Ip,t[2]*=Ip,t[3]&&"number"==typeof t[3]&&(t[3]*=Ip);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Ip+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=Ip,t[2]*=Ip,t[3]&&"number"==typeof t[3]&&(t[3]*=Ip);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Ip+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(Ip,Ip),e.apply(this,arguments),this.scale(1/Ip,1/Ip)}}(t.drawImage))}const zp=he((()=>Dp()));function Rp(e){return e?Lu(e):e}function Np(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function jp(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}let Fp;function qp(e=0,t=0){return Fp||(Fp=document.createElement("canvas")),Fp.width=e,Fp.height=t,Fp}const Vp=vu({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){zp();const o=sn(null),i=sn(null),r=sn(null),a=sn(!1),s=function(e){return(t,n)=>{e(t,Gc(n))}}(t),{$attrs:l,$excludeAttrs:u,$listeners:d}=Ch({excludeListeners:!0}),{_listeners:p}=function(e,t,n){const o=Er((()=>{let o=["onTouchstart","onTouchmove","onTouchend"],i=t.value,r=c({},(()=>{let e={};for(const t in i)if(f(i,t)){const n=i[t];e[t]=n}return e})());return o.forEach((t=>{let o=[];r[t]&&o.push(bu((e=>{const o=e.currentTarget.getBoundingClientRect();jp(o,e.touches),jp(o,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&o.push(gc),r[t]=o})),r}));return{_listeners:o}}(e,d,s),{_handleSubscribe:h,_resize:g}=function(e,t,n){let o=[],i={};const r=Er((()=>e.hidpi?Ip:1));function a(n){let o=t.value;if(!n||o.width!==Math.floor(n.width*r.value)||o.height!==Math.floor(n.height*r.value))if(o.width>0&&o.height>0){let t=o.getContext("2d"),n=t.getImageData(0,0,o.width,o.height);Op(o,e.hidpi),t.putImageData(n,0,0)}else Op(o,e.hidpi)}function s({actions:e,reserve:r},a){if(!e)return;if(n.value)return void o.push([e,r]);let s=t.value,c=s.getContext("2d");r||(c.fillStyle="#000000",c.strokeStyle="#000000",c.shadowColor="#000000",c.shadowBlur=0,c.shadowOffsetX=0,c.shadowOffsetY=0,c.setTransform(1,0,0,1,0,0),c.clearRect(0,0,s.width,s.height)),l(e);for(let t=0;t<e.length;t++){const n=e[t];let o=n.method;const r=n.data,s=r[0];if(/^set/.test(o)&&"setTransform"!==o){const n=o[3].toLowerCase()+o.slice(4);let i;if("fillStyle"===n||"strokeStyle"===n){if("normal"===s)i=Np(r[1]);else if("linear"===s){const e=c.createLinearGradient(...r[1]);r[2].forEach((function(t){const n=t[0],o=Np(t[1]);e.addColorStop(n,o)})),i=e}else if("radial"===s){let e=r[1];const t=e[0],n=e[1],o=e[2],a=c.createRadialGradient(t,n,0,t,n,o);r[2].forEach((function(e){const t=e[0],n=Np(e[1]);a.addColorStop(t,n)})),i=a}else if("pattern"===s){if(!u(r[1],e.slice(t+1),a,(function(e){e&&(c[n]=c.createPattern(e,r[2]))})))break;continue}c[n]=i}else if("globalAlpha"===n)c[n]=Number(s)/255;else if("shadow"===n){let e=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];r.forEach((function(t,n){c[e[n]]="shadowColor"===e[n]?Np(t):t}))}else if("fontSize"===n){const e=c.__font__||c.font;c.__font__=c.font=e.replace(/\d+\.?\d*px/,s+"px")}else"lineDash"===n?(c.setLineDash(s),c.lineDashOffset=r[1]||0):"textBaseline"===n?("normal"===s&&(r[0]="alphabetic"),c[n]=s):"font"===n?c.__font__=c.font=s:c[n]=s}else if("fillPath"===o||"strokePath"===o)o=o.replace(/Path/,""),c.beginPath(),r.forEach((function(e){c[e.method].apply(c,e.data)})),c[o]();else if("fillText"===o)c.fillText.apply(c,r);else if("drawImage"===o){if("break"===function(){let n=[...r],o=n[0],s=n.slice(1);if(i=i||{},!u(o,e.slice(t+1),a,(function(e){e&&c.drawImage.apply(c,[e].concat([...s.slice(4,8)],[...s.slice(0,4)]))})))return"break"}())break}else"clip"===o?(r.forEach((function(e){c[e.method].apply(c,e.data)})),c.clip()):c[o].apply(c,r)}n.value||a({errMsg:"drawCanvas:ok"})}function l(e){e.forEach((function(e){let t=e.method,n=e.data,o="";function r(){const e=i[o]=new Image;e.onload=function(){e.ready=!0},function(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):Tp(e).then(Ep)}(o).then((t=>{e.src=t})).catch((()=>{e.src=o}))}"drawImage"===t?(o=n[0],o=Rp(o),n[0]=o):"setFillStyle"===t&&"pattern"===n[0]&&(o=n[1],o=Rp(o),n[1]=o),o&&!i[o]&&r()}))}function u(e,t,r,a){let l=i[e];return l.ready?(a(l),!0):(o.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,a(l),n.value=!1;let e=o.slice(0);o=[];for(let t=e.shift();t;)s({actions:t[0],reserve:t[1]},r),t=e.shift()},!1)}function d({x:e=0,y:n=0,width:o,height:i,destWidth:a,destHeight:s,hidpi:l=!0,dataType:c,quality:u=1,type:d="png"},f){const p=t.value;let h;const g=p.offsetWidth-e;o=o?Math.min(o,g):g;const m=p.offsetHeight-n;i=i?Math.min(i,m):m,l?(a=o,s=i):a||s?a?s||(s=Math.round(i/o*a)):a=Math.round(o/i*s):(a=Math.round(o*r.value),s=Math.round(i*r.value));const v=qp(a,s),y=v.getContext("2d");let b;"jpeg"!==d&&"jpg"!==d||(d="jpeg",y.fillStyle="#fff",y.fillRect(0,0,a,s)),y.__hidpi__=!0,y.drawImageByCanvas(p,e,n,o,i,0,0,a,s,!1);try{let e;if("base64"===c)h=v.toDataURL(`image/${d}`,u);else{const e=y.getImageData(0,0,a,s);h=Array.prototype.slice.call(e.data)}b={data:h,compressed:e,width:a,height:s}}catch(w){b={errMsg:`canvasGetImageData:fail ${w}`}}if(v.height=v.width=0,y.__hidpi__=!1,!f)return b;f(b)}function f({data:e,x:n,y:o,width:i,height:r,compressed:a},s){try{0,r||(r=Math.round(e.length/4/i));const a=qp(i,r);a.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(e),i,r),0,0),t.value.getContext("2d").drawImage(a,n,o,i,r),a.height=a.width=0}catch(l){return void s({errMsg:"canvasPutImageData:fail"})}s({errMsg:"canvasPutImageData:ok"})}function p({x:e=0,y:t=0,width:n,height:o,destWidth:i,destHeight:r,fileType:a,quality:s,dirname:l},c){const u=d({x:e,y:t,width:n,height:o,destWidth:i,destHeight:r,hidpi:!1,dataType:"base64",type:a,quality:s});var f;u.data&&u.data.length?(f=u.data,((e,t)=>{let n="toTempFilePath:"+(e?"fail":"ok");e&&(n+=` ${e.message}`),c({errMsg:n,tempFilePath:t})})(null,f)):c({errMsg:u.errMsg.replace("canvasPutImageData","toTempFilePath")})}const h={actionsChanged:s,getImageData:d,putImageData:f,toTempFilePath:p};function g(e,t,n){let o=h[e];0!==e.indexOf("_")&&v(o)&&o(t,n)}return c(h,{_resize:a,_handleSubscribe:g})}(e,i,a);return Ig(h,Lg(e.canvasId),!0),jo((()=>{g()})),()=>{const{canvasId:t,disableScroll:a}=e;return rr("uni-canvas",fr({ref:o,"canvas-id":t,"disable-scroll":a},l.value,u.value,p.value),[rr("canvas",{ref:i,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),rr("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),rr(Mp,{ref:r,onResize:g},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});function Hp(){}const Qp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function Up(e,t,n){function o(e){const t=Er((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",Hp,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",Hp,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}oo((()=>t.value),(e=>e&&o(e)))}var Wp=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,$p=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Xp=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Yp=th("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Jp=th("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Gp=th("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Kp=th("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Zp=th("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),eh=th("script,style");function th(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const nh={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},oh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},ih={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},rh=vu({name:"Image",props:nh,setup(e,{emit:t}){const n=sn(null),o=function(e,t){const n=sn(""),o=Er((()=>{let e="auto",o="";const i=ih[t.mode];return i?(i[0]&&(o=i[0]),i[1]&&(e=i[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),i=Wt({rootEl:e,src:Er((()=>t.src?Lu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return jo((()=>{const t=e.value.style;i.origWidth=Number(t.width)||0,i.origHeight=Number(t.height)||0})),i}(n,e),i=wu(n,t),{fixSize:r}=function(e,t,n){const o=()=>{const{mode:o}=t,i=oh[o];if(!i)return;const{origWidth:r,origHeight:a}=n,s=r&&a?r/a:0;if(!s)return;const l=e.value,c=l[i[0]];c&&(l.style[i[1]]=function(e){ah&&e>10&&(e=2*Math.round(e/2));return e}(i[2](c,s))+"px")},i=()=>{const{style:t}=e.value,{origStyle:{width:o,height:i}}=n;t.width=o,t.height=i};return oo((()=>t.mode),((e,t)=>{oh[t]&&i(),oh[e]&&o()})),{fixSize:o,resetSize:i}}(n,e,o);return function(e,t,n,o,i){let r,a;const s=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void s();r=r||new Image,r.onload=e=>{const{width:u,height:d}=r;s(u,d,l),o(),r.draggable=t.draggable,a&&a.remove(),a=r,n.value.appendChild(r),c(),i("load",e,{width:u,height:d})},r.onerror=t=>{s(),c(),i("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},r.src=l},c=()=>{r&&(r.onload=null,r.onerror=null,r=null)};oo((()=>e.src),(e=>l(e))),oo((()=>e.imgSrc),(e=>{!e&&a&&(a.remove(),a=null)})),jo((()=>l(e.src))),Vo((()=>c()))}(o,e,n,r,i),()=>rr("uni-image",{ref:n},[rr("div",{style:o.modeStyle},null,4),oh[e.mode]?rr(Mp,{onResize:r},null,8,["onResize"]):rr("span",null,null)],512)}});const ah="Google Inc."===navigator.vendor;const sh=we(!0),lh=[];let ch,uh=0;const dh=e=>lh.forEach((t=>t.userAction=e));function fh(e={userAction:!1}){if(!ch){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!uh&&dh(!0),uh++,setTimeout((()=>{!--uh&&dh(!1)}),0)}),sh)})),ch=!0}lh.push(e)}const ph=()=>!!uh;function hh(){const e=Wt({userAction:!1});return jo((()=>{fh(e)})),Vo((()=>{!function(e){const t=lh.indexOf(e);t>=0&&lh.splice(t,1)}(e)})),{state:e}}function gh(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function mh(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const vh=["none","text","decimal","numeric","tel","search","email","url"],yh=c({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~vh.indexOf(e)},cursorColor:{type:String,default:""}},Qp),bh=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function wh(e,t,n,o){const i=Ce((n=>{t.value=mh(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});oo((()=>e.modelValue),i),oo((()=>e.value),i);const r=function(e,t){let n,o,i=0;const r=function(...r){const a=Date.now();clearTimeout(n),o=()=>{o=null,i=a,e.apply(this,r)},a-i<t?n=setTimeout(o,t-(a-i)):o()};return r.cancel=function(){clearTimeout(n),o=null},r.flush=function(){clearTimeout(n),o&&o()},r}(((e,t)=>{i.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return No((()=>{i.cancel(),r.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{i.cancel(),r(e,t),n&&r.flush()}}}function xh(e,t){hh();const n=Er((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}oo((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),jo((()=>{n.value&&In(o)}))}function _h(e,t,n,o){Ul(Dc(),"getSelectedTextRange",gh);const{fieldRef:i,state:r,trigger:a}=function(e,t,n){const o=sn(null),i=wu(t,n),r=Er((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),a=Er((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=Er((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Er((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=mh(e.modelValue,e.type)||mh(e.value,e.type),u=Wt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:r,selectionEnd:a,cursor:s});return oo((()=>u.focus),(e=>n("update:focus",e))),oo((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:i}}(e,t,n),{triggerInput:s}=wh(e,r,n,a);xh(e,i),Up(0,i);const{state:l}=function(){const e=Wt({attrs:{}});return jo((()=>{let t=vr();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}();!function(e,t){const n=eo(Su,!1);if(!n)return;const o=vr(),i={submit(){const n=o.proxy;return[n[e],y(t)?n[t]:t.value]},reset(){y(t)?o.proxy[t]="":t.value=""}};n.addField(i),Vo((()=>{n.removeField(i)}))}("name",r),function(e,t,n,o,i,r){function a(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}oo([()=>t.selectionStart,()=>t.selectionEnd],a),oo((()=>t.cursor),s),oo((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),v(r)&&!1===r(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||i(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),a(),s()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(i,r,e,a,s,o);return{fieldRef:i,state:r,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:a}}const Ah=vu({name:"Input",props:c({},yh,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...bh],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],i=["off","one-time-code"],r=Er((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~o.includes(e.type)?e.type:"text"}return e.password?"password":t})),a=Er((()=>{const t=i.indexOf(e.textContentType),n=i.indexOf(M(e.textContentType));return i[-1!==t?t:-1!==n?n:0]}));let s,l=sn("");const c=sn(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:p,trigger:h}=_h(e,c,t,((e,t)=>{const n=e.target;if("number"===r.value){if(s&&(n.removeEventListener("blur",s),s=null),n.validity&&!n.validity.valid){if((!l.value||!n.value)&&"-"===e.data||"-"===l.value[0]&&"deleteContentBackward"===e.inputType)return l.value="-",t.value="",s=()=>{l.value=n.value=""},n.addEventListener("blur",s),!1;if(l.value)if(-1!==l.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=l.value.indexOf(".");return l.value=n.value=t.value=l.value.slice(0,e),!0}}else if("."===e.data)return l.value+=".",s=()=>{l.value=n.value=l.value.slice(0,-1)},n.addEventListener("blur",s),!1;return l.value=t.value=n.value="-"===l.value?"":l.value,!1}l.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));oo((()=>d.value),(t=>{"number"!==e.type||"-"===l.value&&""===t||(l.value=t)}));const g=["number","digit"],m=Er((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&p?rr("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):Xo(rr("input",{key:"input",ref:u,"onUpdate:modelValue":e=>d.value=e,disabled:!!e.disabled,type:r.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:a.value,onKeyup:v,inputmode:e.inputmode},null,44,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[Ca,d.value]]);return rr("uni-input",{ref:c},[rr("div",{class:"uni-input-wrapper"},[Xo(rr("div",fr(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ma,!(d.value.length||"-"===l.value)]]),"search"===e.confirmType?rr("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Sh=["class","style"],Th=/^on[A-Z]+/,Ch=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=vr(),i=ln({}),r=ln({}),a=ln({}),s=n.concat(Sh);return o.attrs=Wt(o.attrs),to((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(s.includes(n)?e.exclude[n]=o:Th.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;i.value=e.attrs,r.value=e.listeners,a.value=e.exclude})),{$attrs:i,$listeners:r,$excludeAttrs:a}};function Eh(e){const t=[];return p(e)&&e.forEach((e=>{Zi(e)?e.type===qi?t.push(...Eh(e.children)):t.push(e):p(e)&&t.push(...Eh(e))})),t}const kh=vu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=sn(null),o=sn(!1);let{setContexts:i,events:r}=function(e,t){const n=sn(0),o=sn(0),i=Wt({x:null,y:null}),r=sn(null);let a=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):a&&a._setScale(t))}function c(e,n=s){let o=t.value;function i(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:i(e.parentNode)}return i(e)}const u=bu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(r.value=Ph(t),i.x=t.x,i.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);a=e&&e===t?e:null}}})),d=bu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==i.x&&r.value&&r.value>0){l(Ph(n)/r.value)}i.x=n.x,i.y=n.y}})),f=bu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(i.x=0,i.y=0,r.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):a&&a._endScale())}));function p(){h(),s.forEach((function(e,t){e.setParent()}))}function h(){let e=window.getComputedStyle(t.value),i=t.value.getBoundingClientRect();n.value=i.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=i.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return Zn("movableAreaWidth",n),Zn("movableAreaHeight",o),{setContexts(e){s=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:f,_resize:p}}}(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=Ch(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=r[`_${e}`];c[e]=t?[].concat(t,n):n})),jo((()=>{r._resize(),o.value=!0}));let u=[];const d=[];function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(en(o))}i(e)}return Zn("_isMounted",o),Zn("movableAreaRootRef",n),Zn("addMovableViewContext",(e=>{d.push(e),f()})),Zn("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())})),()=>{const e=t.default&&t.default();return u=Eh(e),rr("uni-movable-area",fr({ref:n},s.value,l.value,c),[rr(Mp,{onResize:r._resize},null,8,["onResize"]),u],16)}}});function Ph(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const Bh=function(e,t,n,o){e.addEventListener(t,(e=>{v(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Mh,Ih;function Oh(e,t,n){Vo((()=>{document.removeEventListener("mousemove",Mh),document.removeEventListener("mouseup",Ih)}));let o=0,i=0,r=0,a=0;const s=function(e,n,s,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:s,y:l,dx:s-o,dy:l-i,ddx:s-r,ddy:l-a,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Bh(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=r=e.touches[0].pageX,i=a=e.touches[0].pageY,s(e,"start",o,i)})),Bh(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=r=e.pageX,i=a=e.pageY,s(e,"start",o,i)})),Bh(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=s(e,"move",e.touches[0].pageX,e.touches[0].pageY);return r=e.touches[0].pageX,a=e.touches[0].pageY,t}}));const d=Mh=function(e){if(!l&&c&&u){const t=s(e,"move",e.pageX,e.pageY);return r=e.pageX,a=e.pageY,t}};document.addEventListener("mousemove",d),Bh(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,s(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=Ih=function(e){if(c=!1,!l&&u)return u=null,s(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Bh(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,s(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function Lh(e,t,n){return e>t-n&&e<t+n}function Dh(e,t){return Lh(e,0,t)}function zh(){}function Rh(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Nh(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function jh(e,t,n){this._springX=new Nh(e,t,n),this._springY=new Nh(e,t,n),this._springScale=new Nh(e,t,n),this._startTime=0}zh.prototype.x=function(e){return Math.sqrt(e)},Rh.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Rh.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Rh.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Rh.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Rh.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Rh.prototype.dt=function(){return-this._x_v/this._x_a},Rh.prototype.done=function(){const e=Lh(this.s().x,this._endPositionX)||Lh(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Rh.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Rh.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Nh.prototype._solve=function(e,t){const n=this._c,o=this._m,i=this._k,r=n*n-4*o*i;if(0===r){const i=-n/(2*o),r=e,a=t/(i*e);return{x:function(e){return(r+a*e)*Math.pow(Math.E,i*e)},dx:function(e){const t=Math.pow(Math.E,i*e);return i*(r+a*e)*t+a*t}}}if(r>0){const i=(-n-Math.sqrt(r))/(2*o),a=(-n+Math.sqrt(r))/(2*o),s=(t-i*e)/(a-i),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,i*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,i*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*i*t+s*a*n}}}const a=Math.sqrt(4*o*i-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}},Nh.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Nh.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Nh.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Dh(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(Dh(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Dh(t,.1)&&(t=0),Dh(o,.1)&&(o=0),o+=this._endPosition),this._solution&&Dh(o-e,.1)&&Dh(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Nh.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Nh.prototype.done=function(e){return e||(e=(new Date).getTime()),Lh(this.x(),this._endPosition,.1)&&Dh(this.dx(),.1)},Nh.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Nh.prototype.springConstant=function(){return this._k},Nh.prototype.damping=function(){return this._c},Nh.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},jh.prototype.setEnd=function(e,t,n,o){const i=(new Date).getTime();this._springX.setEnd(e,o,i),this._springY.setEnd(t,o,i),this._springScale.setEnd(n,o,i),this._startTime=i},jh.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},jh.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},jh.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Fh(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const qh=vu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=sn(null),i=wu(o,n),{setParent:r}=function(e,t,n){const o=eo("_isMounted",sn(!1)),i=eo("addMovableViewContext",(()=>{})),r=eo("removeMovableViewContext",(()=>{}));let a,s,l=sn(1),c=sn(1),u=sn(!1),d=sn(0),f=sn(0),p=null,h=null,g=!1,m=null,v=null;const y=new zh,b=new zh,w={historyX:[0,0],historyY:[0,0],historyT:[0,0]},x=Er((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),_=new Rh(1,x.value);oo((()=>e.disabled),(()=>{U()}));const{_updateOldScale:A,_endScale:S,_setScale:T,scaleValueSync:C,_updateBoundary:E,_updateOffset:k,_updateWH:P,_scaleOffset:B,minX:M,minY:I,maxX:O,maxY:L,FAandSFACancel:D,_getLimitXY:z,_setTransform:R,_revise:N,dampingNumber:j,xMove:F,yMove:q,xSync:V,ySync:H,_STD:Q}=function(e,t,n,o,i,r,a,s,l,c){const u=Er((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=Er((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),f=sn(Number(e.scaleValue)||1);oo(f,(e=>{R(e)})),oo(u,(()=>{z()})),oo(d,(()=>{z()})),oo((()=>e.scaleValue),(e=>{f.value=Number(e)||0}));const{_updateBoundary:p,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:w}=function(e,t,n){const o=eo("movableAreaWidth",sn(0)),i=eo("movableAreaHeight",sn(0)),r=eo("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=sn(0),c=sn(0),u=sn(0),d=sn(0),f=sn(0),p=sn(0);function h(){let e=0-a.x+s.x,t=o.value-l.value-a.x-s.x;u.value=Math.min(e,t),f.value=Math.max(e,t);let n=0-a.y+s.y,r=i.value-c.value-a.y-s.y;d.value=Math.min(n,r),p.value=Math.max(n,r)}function g(){a.x=Qh(e.value,r.value),a.y=Uh(e.value,r.value)}function m(o){o=o||t.value,o=n(o);let i=e.value.getBoundingClientRect();c.value=i.height/t.value,l.value=i.width/t.value;let r=c.value*o,a=l.value*o;s.x=(a-l.value)/2,s.y=(r-c.value)/2}return{_updateBoundary:h,_updateOffset:g,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:f,maxY:p}}(t,o,D),{FAandSFACancel:x,_getLimitXY:_,_animationTo:A,_setTransform:S,_revise:T,dampingNumber:C,xMove:E,yMove:k,xSync:P,ySync:B,_STD:M}=function(e,t,n,o,i,r,a,s,l,c,u,d,f,p){const h=Er((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=Er((()=>"all"===t.direction||"horizontal"===t.direction)),m=Er((()=>"all"===t.direction||"vertical"===t.direction)),v=sn($h(t.x)),y=sn($h(t.y));oo((()=>t.x),(e=>{v.value=$h(e)})),oo((()=>t.y),(e=>{y.value=$h(e)})),oo(v,(e=>{T(e)})),oo(y,(e=>{C(e)}));const b=new jh(1,9*Math.pow(h.value,2)/40,h.value);function w(e,t){let n=!1;return e>i.value?(e=i.value,n=!0):e<a.value&&(e=a.value,n=!0),t>r.value?(t=r.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function x(){d&&d.cancel(),u&&u.cancel()}function _(e,n,i,r,a,s){x(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(i=o.value);let d=w(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,i,1),u=Wh(b,(function(){let e=b.x();A(e.x,e.y,e.scale,r,a,s)}),(function(){u.cancel()}))):A(e,n,i,r,a,s)}function A(i,r,a,s="",u,d){null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=l.value||0),null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=c.value||0),i=Number(i.toFixed(1)),r=Number(r.toFixed(1)),a=Number(a.toFixed(1)),l.value===i&&c.value===r||u||p("change",{},{x:Fh(i,n.x),y:Fh(r,n.y),source:s}),t.scale||(a=o.value),a=+(a=f(a)).toFixed(3),d&&a!==o.value&&p("scale",{},{x:i,y:r,scale:a});let h="translateX("+i+"px) translateY("+r+"px) translateZ(0px) scale("+a+")";e.value&&(e.value.style.transform=h,e.value.style.webkitTransform=h,l.value=i,c.value=r,o.value=a)}function S(e){let t=w(l.value,c.value),n=t.x,i=t.y,r=t.outOfBounds;return r&&_(n,i,o.value,e),r}function T(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),_(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),_(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:x,_getLimitXY:w,_animationTo:_,_setTransform:A,_revise:S,dampingNumber:h,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,w,v,y,a,s,l,c,D,n);function I(t,n){if(e.scale){t=D(t),g(t),p();const e=_(a.value,s.value),o=e.x,i=e.y;n?A(o,i,t,"",!0,!0):Hh((function(){S(o,i,t,"",!0,!0)}))}}function O(){r.value=!0}function L(e){i.value=e}function D(e){return e=Math.max(.5,u.value,e),e=Math.min(10,d.value,e)}function z(){if(!e.scale)return!1;I(o.value,!0),L(o.value)}function R(t){return!!e.scale&&(I(t=D(t),!0),L(t),t)}function N(){r.value=!1,L(o.value)}function j(e){e&&(e=i.value*e,O(),I(e))}return{_updateOldScale:L,_endScale:N,_setScale:j,scaleValueSync:f,_updateBoundary:p,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:w,FAandSFACancel:x,_getLimitXY:_,_animationTo:A,_setTransform:S,_revise:T,dampingNumber:C,xMove:E,yMove:k,xSync:P,ySync:B,_STD:M}}(e,n,t,l,c,u,d,f,p,h);function U(){u.value||e.disabled||(D(),w.historyX=[0,0],w.historyY=[0,0],w.historyT=[0,0],F.value&&(a=d.value),q.value&&(s=f.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function W(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=f.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),F.value&&(n=t.detail.dx+a,w.historyX.shift(),w.historyX.push(n),q.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),q.value&&(o=t.detail.dy+s,w.historyY.shift(),w.historyY.push(o),F.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),w.historyT.shift(),w.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let i="touch";n<M.value?e.outOfBounds?(i="touch-out-of-bounds",n=M.value-y.x(M.value-n)):n=M.value:n>O.value&&(e.outOfBounds?(i="touch-out-of-bounds",n=O.value+y.x(n-O.value)):n=O.value),o<I.value?e.outOfBounds?(i="touch-out-of-bounds",o=I.value-b.x(I.value-o)):o=I.value:o>L.value&&(e.outOfBounds?(i="touch-out-of-bounds",o=L.value+b.x(o-L.value)):o=L.value),Hh((function(){R(n,o,l.value,i)}))}}}function $(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(w.historyX[1]-w.historyX[0])/(w.historyT[1]-w.historyT[0]),t=1e3*(w.historyY[1]-w.historyY[0])/(w.historyT[1]-w.historyT[0]),n=d.value,o=f.value;_.setV(e,t),_.setS(n,o);const i=_.delta().x,r=_.delta().y;let a=i+n,s=r+o;a<M.value?(a=M.value,s=o+(M.value-n)*r/i):a>O.value&&(a=O.value,s=o+(O.value-n)*r/i),s<I.value?(s=I.value,a=n+(I.value-o)*i/r):s>L.value&&(s=L.value,a=n+(L.value-o)*i/r),_.setEnd(a,s),h=Wh(_,(function(){let e=_.s(),t=e.x,n=e.y;R(t,n,l.value,"friction")}),(function(){h.cancel()}))}e.outOfBounds||e.inertia||D()}function X(){if(!o.value)return;D();let t=e.scale?C.value:1;k(),P(t),E();let n=z(V.value+B.x,H.value+B.y),i=n.x,r=n.y;R(i,r,t,"",!0),A(t)}return jo((()=>{Oh(n.value,(e=>{switch(e.detail.state){case"start":U();break;case"move":W(e);break;case"end":$()}})),X(),_.reconfigure(1,x.value),Q.reconfigure(1,9*Math.pow(j.value,2)/40,j.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:S,_setScale:T};i(e),Ho((()=>{r(e)}))})),Ho((()=>{D()})),{setParent:X}}(e,i,o);return()=>rr("uni-movable-view",{ref:o},[rr(Mp,{onResize:r},null,8,["onResize"]),t.default&&t.default()],512)}});let Vh=!1;function Hh(e){Vh||(Vh=!0,requestAnimationFrame((function(){e(),Vh=!1})))}function Qh(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Qh(e.offsetParent,t):0}function Uh(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Uh(e.offsetParent,t):0}function Wh(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,i){if(!t||!t.cancelled){o(n);let r=n.done();r||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,i))),r&&i&&i(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function $h(e){return/\d+[ur]px$/i.test(e)?Vd(parseFloat(e)):Number(e)||0}const Xh=["navigate","redirect","switchTab","reLaunch","navigateBack"],Yh=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],Jh=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],Gh={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~Xh.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||Yh.concat(Jh).includes(e)},animationDuration:{type:[String,Number],default:300}};c({},Gh,{renderLink:{type:Boolean,default:!0}});class Kh{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Zh(e,t,n){return e>t-n&&e<t+n}function eg(e,t){return Zh(e,0,t)}class tg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,i=this._k,r=n*n-4*o*i;if(0===r){const i=-n/(2*o),r=e,a=t/(i*e);return{x:function(e){return(r+a*e)*Math.pow(Math.E,i*e)},dx:function(e){const t=Math.pow(Math.E,i*e);return i*(r+a*e)*t+a*t}}}if(r>0){const i=(-n-Math.sqrt(r))/(2*o),a=(-n+Math.sqrt(r))/(2*o),s=(t-i*e)/(a-i),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,i*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,i*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*i*t+s*a*n}}}const a=Math.sqrt(4*o*i-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!eg(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(eg(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),eg(t,.4)&&(t=0),eg(o,.4)&&(o=0),o+=this._endPosition),this._solution&&eg(o-e,.4)&&eg(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Zh(this.x(),this._endPosition,.4)&&eg(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class ng{constructor(e,t,n){this._extent=e,this._friction=t||new Kh(.01),this._spring=n||new tg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class og{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new ng(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,i){if(!t||!t.cancelled){o(n);const r=n.done();r||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,i))),r&&i&&i(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(v(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const i=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),i!==this._position&&(this.dispatchScroll(),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const ig=_c("ucg"),rg=vu({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,{emit:t,slots:n}){const o=sn(null);return function(e,t){const n=[];jo((()=>{s(n.length-1)}));const o=()=>{var e;return null==(e=n.find((e=>e.value.radioChecked)))?void 0:e.value.value};Zn(ig,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},radioChange(e,i){s(n.indexOf(i),!0),t("change",e,{value:o()})}});const i=eo(Su,!1),r={submit:()=>{let t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=o()),t}};i&&(i.addField(r),Vo((()=>{i.removeField(r)})));function a(e,t){e.value={radioChecked:t,value:e.value.value}}function s(e,t){n.forEach(((o,i)=>{i!==e&&(t?a(n[i],!1):n.forEach(((e,t)=>{i>=t||n[t].value.radioChecked&&a(n[i],!1)})))}))}}(e,wu(o,t)),()=>rr("uni-radio-group",{ref:o},[n.default&&n.default()],512)}});const ag=vu({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,{slots:t}){const n=sn(null),o=sn(e.checked),i=sn(e.value);const r=Er((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const n={};return o.value?(n.backgroundColor=e.activeBackgroundColor||e.color,n.borderColor=e.activeBorderColor||n.backgroundColor):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(o.value)));oo([()=>e.checked,()=>e.value],(([e,t])=>{o.value=e,i.value=t}));const{uniCheckGroup:a,uniLabel:s,field:l}=function(e,t,n){const o=Er({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:({radioChecked:t})=>{e.value=t}}),i={reset:n},r=eo(ig,!1);r&&r.addField(o);const a=eo(Su,!1);a&&a.addField(i);const s=eo(Cu,!1);return Vo((()=>{r&&r.removeField(o),a&&a.removeField(i)})),{uniCheckGroup:r,uniForm:a,uniLabel:s,field:o}}(o,i,(()=>{o.value=!1})),c=t=>{e.disabled||o.value||(o.value=!0,a&&a.radioChange(t,l),t.stopPropagation())};return s&&(s.addHandler(c),Vo((()=>{s.removeHandler(c)}))),ku(e,{"label-click":c}),()=>{const i=Au(e,"disabled");let a;return a=o.value,rr("uni-radio",fr(i,{id:e.id,onClick:c,ref:n}),[rr("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":o.value?r.value.borderColor:e.activeBorderColor}},[rr("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:r.value},[a?Bc(Tc,e.disabled?"#ADADAD":e.iconColor,18):""],6),t.default&&t.default()],4)],16,["id","onClick"])}}});const sg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},lg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const cg=(e,t,n)=>!n||p(n)&&!n.length?[]:n.map((n=>{if(S(n)){if(!f(n,"type")||"node"===n.type){let o={[e]:""};const i=n.name.toLowerCase();if(!f(sg,i))return;return function(e,t){if(S(t))for(const n in t)if(f(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Lu(o))}}(i,n.attrs),o=c(o,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Pr(n.name,o,cg(e,t,n.children))}return"text"===n.type&&y(n.text)&&""!==n.text?sr((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return f(lg,t)&&lg[t]?lg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function ug(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,i,r=[],a=e;for(r.last=function(){return this[this.length-1]};e;){if(o=!0,r.last()&&eh[r.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+r.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",r.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(i=e.match($p))&&(e=e.substring(i[0].length),i[0].replace($p,c),o=!1):0==e.indexOf("<")&&(i=e.match(Wp))&&(e=e.substring(i[0].length),i[0].replace(Wp,l),o=!1),o){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==a)throw"Parse Error: "+e;a=e}function l(e,n,o,i){if(n=n.toLowerCase(),Jp[n])for(;r.last()&&Gp[r.last()];)c("",r.last());if(Kp[n]&&r.last()==n&&c("",n),(i=Yp[n]||!!i)||r.push(n),t.start){var a=[];o.replace(Xp,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Zp[t]?t:"";a.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,a,i)}}function c(e,n){if(n)for(o=r.length-1;o>=0&&r[o]!=n;o--);else var o=0;if(o>=0){for(var i=r.length-1;i>=o;i--)t.end&&t.end(r[i]);r.length=o}}c()}(e,{start:function(e,o,i){const r={name:e};if(0!==o.length&&(r.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),i){const e=t[0]||n;e.children||(e.children=[]),e.children.push(r)}else t.unshift(r)},end:function(e){const o=t.shift();if(o.name,0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o.children||(o.children=[]),o.children.push(n)}}),n.children}const dg=vu({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,{emit:t}){const n=vr(),o=n&&n.vnode.scopeId||"",i=sn(null),r=sn([]),a=wu(i,t);function s(e,t={}){a("itemclick",e,t)}return oo((()=>e.nodes),(function(){let t=e.nodes;y(t)&&(t=ug(e.nodes)),r.value=cg(o,s,t)}),{immediate:!0}),()=>Pr("uni-rich-text",{ref:i},Pr("div",{},r.value))}}),fg=we(!0),pg=vu({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=sn(null),i=sn(null),r=sn(null),a=sn(null),s=sn(null),l=wu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=Er((()=>Number(e.scrollTop)||0)),n=Er((()=>Number(e.scrollLeft)||0));return{state:Wt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p}=function(e,t,n,o,i,r,a,s,l){let c=!1,u=0,d=!1,f=()=>{};const p=Er((()=>e.scrollX)),h=Er((()=>e.scrollY)),g=Er((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=Er((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=a.value;let o=0,i="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let r=s.value;r.style.transition="transform .3s ease-out",r.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?i="translateX("+o+"px) translateZ(0)":"y"===t&&(i="translateY("+o+"px) translateZ(0)"),r.removeEventListener("transitionend",f),r.removeEventListener("webkitTransitionEnd",f),f=()=>_(e,t),r.addEventListener("transitionend",f),r.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),r.style.transform=i,r.style.webkitTransform=i}function y(e){const n=e.target;i("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),p.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):a.value.scrollTop=t)}function w(t){p.value&&(e.scrollWithAnimation?v(t,"x"):a.value.scrollLeft=t)}function x(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return;let n=r.value.querySelector("#"+t);if(n){let t=a.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(p.value){let n=o.left-t.left,i=a.value.scrollLeft+n;e.scrollWithAnimation?v(i,"x"):a.value.scrollLeft=i}if(h.value){let n=o.top-t.top,i=a.value.scrollTop+n;e.scrollWithAnimation?v(i,"y"):a.value.scrollTop=i}}}}function _(e,t){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let n=a.value;"x"===t?(n.style.overflowX=p.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),s.value.removeEventListener("transitionend",f),s.value.removeEventListener("webkitTransitionEnd",f)}function A(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,i("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,i("refresherrestore",{},{})),"refresherabort"===n&&d&&(d=!1,i("refresherabort",{},{}))}t.refreshState=n}}return jo((()=>{In((()=>{b(n.value),w(o.value)})),x(e.scrollIntoView);let r=function(e){e.preventDefault(),e.stopPropagation(),y(e)},s={x:0,y:0},l=null,f=function(n){if(null===s)return;let o=n.touches[0].pageX,r=n.touches[0].pageY,f=a.value;if(Math.abs(o-s.x)>Math.abs(r-s.y))if(p.value){if(0===f.scrollLeft&&o>s.x)return void(l=!1);if(f.scrollWidth===f.offsetWidth+f.scrollLeft&&o<s.x)return void(l=!1);l=!0}else l=!1;else if(h.value)if(0===f.scrollTop&&r>s.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(f.scrollHeight===f.offsetHeight+f.scrollTop&&r<s.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===f.scrollTop&&1===n.touches.length&&A("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=r-s.y;0===u&&(u=r),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=r-u,t.refresherHeight>0&&(d=!0,i("refresherpulling",n,{deltaY:o})));const a=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(a>1?1:a)}},g=function(e){1===e.touches.length&&(s={x:e.touches[0].pageX,y:e.touches[0].pageY})},m=function(n){s=null,t.refresherHeight>=e.refresherThreshold?A("refreshing"):A("refresherabort")};a.value.addEventListener("touchstart",g,fg),a.value.addEventListener("touchmove",f,we(!1)),a.value.addEventListener("scroll",r,we(!1)),a.value.addEventListener("touchend",m,fg),Vo((()=>{a.value.removeEventListener("touchstart",g),a.value.removeEventListener("touchmove",f),a.value.removeEventListener("scroll",r),a.value.removeEventListener("touchend",m)}))})),Eo((()=>{h.value&&(a.value.scrollTop=t.lastScrollTop),p.value&&(a.value.scrollLeft=t.lastScrollLeft)})),oo(n,(e=>{b(e)})),oo(o,(e=>{w(e)})),oo((()=>e.scrollIntoView),(e=>{x(e)})),oo((()=>e.refresherTriggered),(e=>{!0===e?A("refreshing"):!1===e&&A("restore")})),{realScrollX:p,realScrollY:h}}(e,c,u,d,l,o,i,a,t),h=Er((()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),g=Er((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:d,refreshState:f,refreshRotate:p}=c;return rr("uni-scroll-view",{ref:o},[rr("div",{ref:r,class:"uni-scroll-view"},[rr("div",{ref:i,style:h.value,class:g.value},[rr("div",{ref:a,class:"uni-scroll-view-content"},[t?rr("div",{ref:s,style:{backgroundColor:l,height:d+"px"},class:"uni-scroll-view-refresher"},["none"!==u?rr("div",{class:"uni-scroll-view-refresh"},[rr("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==f?rr("svg",{key:"refresh__icon",style:{transform:"rotate("+p+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[rr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),rr("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==f?rr("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[rr("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],6)],512)],512)}}});const hg=vu({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,{emit:t}){const n=sn(null),o=sn(null),i=sn(null),r=sn(Number(e.value));oo((()=>e.value),(e=>{r.value=Number(e)}));const a=wu(n,t),s=function(e,t){const n=()=>{return n=t.value,o=e.min,i=e.max,i=Number(i),o=Number(o),100*(n-o)/(i-o)+"%";var n,o,i},o=()=>"#e9e9e9"!==e.backgroundColor?e.backgroundColor:"#007aff"!==e.color?e.color:"#007aff",i=()=>"#007aff"!==e.activeColor?e.activeColor:"#e9e9e9"!==e.selectedColor?e.selectedColor:"#e9e9e9";return{setBgColor:Er((()=>({backgroundColor:o()}))),setBlockBg:Er((()=>({left:n()}))),setActiveColor:Er((()=>({backgroundColor:i(),width:n()}))),setBlockStyle:Er((()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor})))}}(e,r),{_onClick:l,_onTrack:c}=function(e,t,n,o,i){const r=n=>{e.disabled||(s(n),i("change",n,{value:t.value}))},a=t=>{const n=Number(e.max),o=Number(e.min),i=Number(e.step);return t<o?o:t>n?n:gg.mul.call(Math.round((t-o)/i),i)+o},s=i=>{const r=Number(e.max),s=Number(e.min),l=o.value,c=getComputedStyle(l,null).marginLeft;let u=l.offsetWidth;u+=parseInt(c);const d=n.value,f=d.offsetWidth-(e.showValue?u:0),p=d.getBoundingClientRect().left,h=(i.x-p)*(r-s)/f+s;t.value=a(h)},l=n=>{if(!e.disabled)return"move"===n.detail.state?(s({x:n.detail.x}),i("changing",n,{value:t.value}),!1):"end"===n.detail.state&&i("change",n,{value:t.value})},c=eo(Su,!1);if(c){const n={reset:()=>t.value=Number(e.min),submit:()=>{const n=["",null];return""!==e.name&&(n[0]=e.name,n[1]=t.value),n}};c.addField(n),Vo((()=>{c.removeField(n)}))}return{_onClick:r,_onTrack:l}}(e,r,n,o,a);return jo((()=>{Oh(i.value,c)})),()=>{const{setBgColor:t,setBlockBg:a,setActiveColor:c,setBlockStyle:u}=s;return rr("uni-slider",{ref:n,onClick:bu(l)},[rr("div",{class:"uni-slider-wrapper"},[rr("div",{class:"uni-slider-tap-area"},[rr("div",{style:t.value,class:"uni-slider-handle-wrapper"},[rr("div",{ref:i,style:a.value,class:"uni-slider-handle"},null,4),rr("div",{style:u.value,class:"uni-slider-thumb"},null,4),rr("div",{style:c.value,class:"uni-slider-track"},null,4)],4)]),Xo(rr("span",{ref:o,class:"uni-slider-value"},[r.value],512),[[Ma,e.showValue]])]),rr("slot",null,null)],8,["onClick"])}}});var gg={mul:function(e){let t=0,n=this.toString(),o=e.toString();try{t+=n.split(".")[1].length}catch(i){}try{t+=o.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(o.replace(".",""))/Math.pow(10,t)}};function mg(e,t,n,o,i,r){function a(){c&&(clearTimeout(c),c=null)}let s,l,c=null,u=!0,d=0,f=1,p=null,h=!1,g=0,m="";const v=Er((()=>n.value.length>t.displayMultipleItems)),y=Er((()=>e.circular&&v.value));function b(i){Math.floor(2*d)===Math.floor(2*i)&&Math.ceil(2*d)===Math.ceil(2*i)||y.value&&function(o){if(!u)for(let i=n.value,r=i.length,a=o+t.displayMultipleItems,s=0;s<r;s++){const t=i[s],n=Math.floor(o/r)*r+s,l=n+r,c=n-r,u=Math.max(o-(n+1),n-a,0),d=Math.max(o-(l+1),l-a,0),f=Math.max(o-(c+1),c-a,0),p=Math.min(u,d,f),h=[n,l,c][[u,d,f].indexOf(p)];t.updatePosition(h,e.vertical)}}(i);const a="translate("+(e.vertical?"0":100*-i*f+"%")+", "+(e.vertical?100*-i*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=a,l.style.transform=a),d=i,!s){if(i%1==0)return;s=i}i-=Math.floor(s);const c=n.value;i<=-(c.length-1)?i+=c.length:i>=c.length&&(i-=c.length),i=s%1>.5||s<0?i-1:i,r("transition",{},{dx:e.vertical?0:i*l.offsetWidth,dy:e.vertical?i*l.offsetHeight:0})}function w(e){const o=n.value.length;if(!o)return-1;const i=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(i>o-t.displayMultipleItems)return o-t.displayMultipleItems;return i}function x(){p=null}function _(){if(!p)return void(h=!1);const e=p,o=e.toPos,i=e.acc,a=e.endTime,c=e.source,u=a-Date.now();if(u<=0){b(o),p=null,h=!1,s=null;const e=n.value[t.current];if(e){const n=e.getItemId();r("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+i*u*u/2),l=requestAnimationFrame(_)}function A(e,o,i){x();const r=t.duration,a=n.value.length;let s=d;if(y.value)if(i<0){for(;s<e;)s+=a;for(;s-a>e;)s-=a}else if(i>0){for(;s>e;)s-=a;for(;s+a<e;)s+=a;s+a-e<e-s&&(s+=a)}else{for(;s+a<e;)s+=a;for(;s-a>e;)s-=a;s+a-e<e-s&&(s+=a)}else"click"===o&&(e=e+t.displayMultipleItems-1<a?e:0);p={toPos:e,acc:2*(s-e)/(r*r),endTime:Date.now()+r,source:o},h||(h=!0,l=requestAnimationFrame(_))}function S(){a();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=w(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,A(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function T(e){e?S():a()}return oo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,i=n.value;t<i.length;t++){if(i[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),oo([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){a(),p&&(b(p.toPos),p=null);const i=n.value;for(let t=0;t<i.length;t++)i[t].updatePosition(t,e.vertical);f=1;const r=o.value;if(1===t.displayMultipleItems&&i.length){const e=i[0].getBoundingClientRect(),t=r.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const s=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(s+l-g),g=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))})),oo((()=>t.interval),(()=>{c&&(a(),S())})),oo((()=>t.current),((e,o)=>{!function(e,o){const i=m;m="";const a=n.value;if(!i){const t=a.length;A(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const s=a[e];if(s){const e=t.currentItemId=s.getItemId();r("change",{},{current:t.current,currentItemId:e,source:i})}}(e,o),i("update:current",e)})),oo((()=>t.currentItemId),(e=>{i("update:currentItemId",e)})),oo((()=>e.autoplay&&!t.userTracking),T),T(e.autoplay&&!t.userTracking),jo((()=>{let i=!1,r=0,s=0;function l(e){t.userTracking=!1;const n=r/Math.abs(r);let o=0;!e&&Math.abs(r)>.2&&(o=.5*n);const i=w(d+o);e?b(g):(m="touch",t.current=i,A(i,"touch",0!==o?o:0===i&&y.value&&d>=1?1:0))}Oh(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,i=!1,a(),g=d,r=0,s=Date.now(),void x();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!i){i=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(i){const a=s;s=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;r=.6*r+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),r=0),b(n)}const d=s-a||1,f=o.value;e.vertical?u(-i.dy/f.offsetHeight,-i.ddy/d):u(-i.dx/f.offsetWidth,-i.ddx/d)}(c.detail),!1}}}))})),Ho((()=>{a(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){A(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const vg=vu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=sn(null),i=wu(o,n),r=sn(null),a=sn(null),s=function(e){return Wt({interval:Er((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:Er((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:Er((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=Er((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Sc(e.previousMargin,!0),bottom:Sc(e.nextMargin,!0)}:{top:0,bottom:0,left:Sc(e.previousMargin,!0),right:Sc(e.nextMargin,!0)}),t})),c=Er((()=>{const t=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],f=sn([]);function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(en(o))}f.value=e}Zn("addSwiperContext",(function(e){d.push(e),p()}));Zn("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=mg(e,s,f,a,n,i);let v=()=>null;return v=yg(o,e,s,h,f,g,m),()=>{const n=t.default&&t.default();return u=Eh(n),rr("uni-swiper",{ref:o},[rr("div",{ref:r,class:"uni-swiper-wrapper"},[rr("div",{class:"uni-swiper-slides",style:l.value},[rr("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&rr("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,o)=>rr("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<s.current+s.displayMultipleItems&&n>=s.current||n<s.current+s.displayMultipleItems-o.length},style:{background:n===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),yg=(e,t,n,o,i,r,a)=>{let s=!1,l=!1,u=!1,d=sn(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}to((()=>{s="auto"===t.navigation,d.value=!0!==t.navigation||s,b()})),to((()=>{const e=i.value.length,t=!r.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,a.value||(l=!0,u=!0,s&&(d.value=!0))}));const p={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function h(e,t,a){if(e.stopPropagation(),a)return;const s=i.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&r.value&&(l=s-1);break;case"next":l++,l>=s&&r.value&&(l=0)}o(l)}const g=()=>Bc(Ec,t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:i}=n,{left:r,right:a,top:s,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(i-s<u/3||l-i<u/3):!(o-r<c/3||a-o<c/3),f)return m=setTimeout((()=>{d.value=f}),300);d.value=f},y=()=>{d.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),s&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return jo(b),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?rr(qi,null,[rr("div",fr({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},p),[g()],16,["onClick"]),rr("div",fr({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>h(e,"next",u)},p),[g()],16,["onClick"])]):null}},bg=vu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=sn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",i=t?100*e+"%":"0",r=n.value,a=`translate(${o},${i}) translateZ(0)`;r&&(r.style.webkitTransform=a,r.style.transform=a)}};return jo((()=>{const e=eo("addSwiperContext");e&&e(o)})),Ho((()=>{const e=eo("removeSwiperContext");e&&e(o)})),()=>rr("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),wg=vu({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=sn(null),o=sn(e.checked),i=function(e,t){const n=eo(Su,!1),o=eo(Cu,!1),i={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(i),Ho((()=>{n.removeField(i)})));return o}(e,o),r=wu(n,t);oo((()=>e.checked),(e=>{o.value=e}));const a=t=>{e.disabled||(o.value=!o.value,r("change",t,{value:o.value}))};return i&&(i.addHandler(a),Vo((()=>{i.removeHandler(a)}))),ku(e,{"label-click":a}),()=>{const{color:t,type:i}=e,r=Au(e,"disabled"),s={};let l;return t&&o.value&&(s.backgroundColor=t,s.borderColor=t),l=o.value,rr("uni-switch",fr({id:e.id,ref:n},r,{onClick:a}),[rr("div",{class:"uni-switch-wrapper"},[Xo(rr("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:s},null,6),[[Ma,"switch"===i]]),Xo(rr("div",{class:"uni-checkbox-input"},[l?Bc(Tc,e.color,22):""],512),[[Ma,"checkbox"===i]])])],16,["id","onClick"])}}});const xg={ensp:" ",emsp:" ",nbsp:" "};function _g(e,t){return function(e,{space:t,decode:n}){let o="",i=!1;for(let r of e)t&&xg[t]&&" "===r&&(r=xg[t]),i?(o+="n"===r?"\n":"\\"===r?"\\":"\\"+r,i=!1):"\\"===r?i=!0:o+=r;return n?o.replace(/&nbsp;/g,xg.nbsp).replace(/&ensp;/g,xg.ensp).replace(/&emsp;/g,xg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const Ag=vu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Hi){const n=_g(t.children,{space:e.space,decode:e.decode}),i=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(sr(e)),t!==i&&o.push(rr("br"))}))}else o.push(t)})),rr("uni-text",{ref:n,selectable:!!e.selectable||null},[rr("span",null,o)],8,["selectable"])}}}),Sg=c({},yh,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>Cg.concat("return").includes(e)}});let Tg=!1;const Cg=["done","go","next","search","send"];const Eg=vu({name:"Textarea",props:Sg,emits:["confirm","linechange",...bh],setup(e,{emit:t,expose:n}){const o=sn(null),i=sn(null),{fieldRef:r,state:a,scopedAttrsState:s,fixDisabledColor:l,trigger:c}=_h(e,o,t),u=Er((()=>a.value.split("\n"))),d=Er((()=>Cg.includes(e.confirmType))),f=sn(0),p=sn(null);function h({height:e}){f.value=e}function g(e){"Enter"===e.key&&d.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&d.value){!function(e){c("confirm",e,{value:a.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return oo((()=>f.value),(t=>{const n=o.value,r=p.value,a=i.value;let s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=r.offsetHeight);var l=Math.round(t/s);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",a.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";Tg=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),a.value=e.value}}),()=>{let t=e.disabled&&l?rr("textarea",{key:"disabled-textarea",ref:r,value:a.value,tabindex:"-1",readonly:!!e.disabled,maxlength:a.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Tg},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):rr("textarea",{key:"textarea",ref:r,value:a.value,disabled:!!e.disabled,maxlength:a.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Tg},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return rr("uni-textarea",{ref:o},[rr("div",{ref:i,class:"uni-textarea-wrapper"},[Xo(rr("div",fr(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ma,!a.value.length]]),rr("div",{ref:p,class:"uni-textarea-line"},[" "],512),rr("div",{class:"uni-textarea-compute"},[u.value.map((e=>rr("div",null,[e.trim()?e:"."]))),rr(Mp,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?rr("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),kg=vu({name:"View",props:c({},xu),setup(e,{slots:t}){const n=sn(null),{hovering:o,binding:i}=_u(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?rr("uni-view",fr({class:o.value?r:"",ref:n},i),[t.default&&t.default()],16):rr("uni-view",{ref:n},[t.default&&t.default()],512)}}});function Pg(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Bg(e,t,n){e&&Ul(n||Dc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function Mg(e,t){e&&function(e,t){t=Ql(e,t),delete Hl[t]}(t||Dc(),e)}function Ig(e,t,n,o){const i=vr().proxy;jo((()=>{Bg(t||Pg(i),e,o),!n&&t||oo((()=>i.id),((t,n)=>{Bg(Pg(i,t),e,o),Mg(n&&Pg(i,n))}))})),Vo((()=>{Mg(t||Pg(i),o)}))}let Og=0;function Lg(e){const t=Mc(),n=vr().proxy,o=n.$options.name.toLowerCase(),i=e||n.id||"context"+Og++;return jo((()=>{n.$el.__uniContextInfo={id:i,type:o,page:t}})),`${o}.${i}`}function Dg(e,t,n,o){v(t)&&zo(e,t.bind(n),o)}function zg(e,t,n){var o;const i=e.mpType||n.$mpType;if(i&&"component"!==i&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!v(t))&&(Be.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const i=e[o];p(i)?i.forEach((e=>Dg(o,e,n,t))):Dg(o,i,n,t)}})),"page"===i)){t.__isVisible=!0;try{jc(n,"onLoad",t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&jc(n,"onShow")}catch(r){}}}function Rg(e,t,n){zg(e,t,n)}function Ng(e,t,n){return e[t]=n}function jg(e,...t){const n=this[e];return n?n(...t):null}function Fg(e){return function(t,n,o){if(!n)throw t;const i=e._instance;if(!i||!i.proxy)throw t;jc(i.proxy,"onError",t)}}function qg(e,t){return e?[...new Set([].concat(e,t))]:t}function Vg(e){const t=e._context.config;var n;t.errorHandler=Ie(e,Fg),n=t.optionMergeStrategies,Be.forEach((e=>{n[e]=qg}));const o=t.globalProperties;o.$set=Ng,o.$applyOptions=Rg,o.$callMethod=jg,function(e){Me.forEach((t=>t(e)))}(e)}const Hg=_c("upm");function Qg(){return eo(Hg)}function Ug(e){const t=function(e){return Wt(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=Sc(e.offset)),e.height&&(e.height=Sc(e.height)),e.range&&(e.range=Sc(e.range)),e}(c({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:i}=n;"custom"!==i&&"transparent"!==o&&(t.offset+=44+hc.top),e.pullToRefresh=t}}{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:i}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=i||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==rm().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(Nc(Sl().meta,e)))))}(e);return Zn(Hg,t),t}function Wg(){return Sl()}function $g(){return history.state&&history.state.__id__||1}let Xg;function Yg(){var e;return Xg||(Xg=__uniConfig.tabBar&&Wt((e=__uniConfig.tabBar,Tl()&&e.list&&e.list.forEach((e=>{Pl(e,["text"])})),e))),Xg}const Jg=window.CSS&&window.CSS.supports;function Gg(e){return Jg&&(Jg(e)||Jg.apply(window.CSS,e.split(":")))}const Kg=Gg("top:env(a)"),Zg=Gg("top:constant(a)"),em=Gg("backdrop-filter:blur(10px)"),tm=(()=>Kg?"env":Zg?"constant":"")();function nm(e){return tm?`calc(${e}px + ${tm}(safe-area-inset-bottom))`:`${e}px`}const om=new Map;function im(){return om}function rm(){const e=[],t=om.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function am(e,t=!0){const n=om.get(e);n.$.__isUnload=!0,jc(n,"onUnload"),om.delete(e),t&&function(e){const t=dm.get(e);t&&(dm.delete(e),fm.pruneCacheEntry(t))}(e)}let sm=$g();function lm(e){const t=Qg();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,i,r){const{id:a,route:s}=o,l=ze(o.navigationBar,__uniConfig.themeConfig,r).titleColor;return{id:a,path:de(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:i,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function cm(e){const t=lm(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),om.set(um(t.path,t.id),e)}function um(e,t){return e+"$$"+t}const dm=new Map,fm={get:e=>dm.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;fm.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;fm.delete(n),fm.pruneCacheEntry(e),In((()=>{om.forEach(((e,t)=>{e.$.isUnmounted&&om.delete(t)}))}))}}))}(e),dm.set(e,t)},delete(e){dm.get(e)&&dm.delete(e)},forEach(e){dm.forEach(e)}};function pm(e,t){!function(e){const t=gm(e),{body:n}=document;mm&&n.removeAttribute(mm),t&&n.setAttribute(t,""),mm=t}(e),function(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=Yg();e.shown&&(n=parseInt(e.height))}var o;xc({"--window-top":(o=t,tm?`calc(${o}px + ${tm}(safe-area-inset-top))`:`${o}px`),"--window-bottom":nm(n)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",Fc),vm&&document.removeEventListener("scroll",vm);if(t.disableScroll)return document.addEventListener("touchmove",Fc);const{onPageScroll:n,onReachBottom:o}=e,i="transparent"===t.navigationBar.type;if(!n&&!o&&!i)return;const r={},a=e.proxy.$page.id;(n||i)&&(r.onPageScroll=function(e,t,n){return o=>{t&&nw.publishHandler("onPageScroll",{scrollTop:o},e),n&&nw.emit(e+".onPageScroll",{scrollTop:o})}}(a,n,i));o&&(r.onReachBottomDistance=t.onReachBottomDistance||50,r.onReachBottom=()=>nw.publishHandler("onReachBottom",{},a));vm=Hc(r),requestAnimationFrame((()=>document.addEventListener("scroll",vm)))}(e,t)}function hm(e){const t=gm(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function gm(e){return e.type.__scopeId}let mm,vm;function ym(e){const t=_l({history:wm(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:bm});e.router=t,e.use(t)}const bm=(e,t,n)=>{if(n)return n};function wm(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=ys(e);return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=rm(),n=t.length-1,o=n-e;for(let i=n;i>o;i--){const e=t[i].$page;am(um(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const xm={install(e){Vg(e),tu(e),fu(e),e.config.warnHandler||(e.config.warnHandler=_m),ym(e)}};function _m(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n)}const Am={class:"uni-async-loading"},Sm=rr("i",{class:"uni-loading"},null,-1),Tm=yu({name:"AsyncLoading",render:()=>($i(),Ki("div",Am,[Sm]))});function Cm(){window.location.reload()}const Em=yu({name:"AsyncError",setup(){Il();const{t:e}=Bl();return()=>rr("div",{class:"uni-async-error",onClick:Cm},[e("uni.async.error")],8,["onClick"])}});let km;function Pm(){return km}function Bm(e){km=e,Object.defineProperty(km.$.ctx,"$children",{get:()=>rm().map((e=>e.$vm))});const t=km.$.appContext.app;t.component(Tm.name)||t.component(Tm.name,Tm),t.component(Em.name)||t.component(Em.name,Em),function(e){e.$vm=e,e.$mpType="app";const t=sn(Bl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(km),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(km),uu(),oc()}function Mm(e,{clone:t,init:n,setup:o,before:i}){t&&(e=c({},e)),i&&i(e);const r=e.setup;return e.setup=(e,t)=>{const i=vr();n(i.proxy);const a=o(i);if(r)return r(a||e,t)},e}function Im(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Mm(e.default,t):Mm(e,t)}function Om(e){return Im(e,{clone:!0,init:cm,setup(e){e.$pageInstance=e;const t=Wg(),n=Ae(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n,e.proxy.options=n;const o=Qg();var i,r,a;return No((()=>{pm(e,o)})),jo((()=>{hm(e);const{onReady:n}=e;n&&D(n),Rm(t)})),Po((()=>{if(!e.__isVisible){pm(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&D(n),In((()=>{Rm(t)}))}}),"ba",i),function(e,t){Po(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&D(t)}})),r=o.id,nw.subscribe(Ql(r,"invokeViewApi"),a?a(Wl):Wl),Vo((()=>{!function(e){nw.unsubscribe(Ql(e,"invokeViewApi")),Object.keys(Hl).forEach((t=>{0===t.indexOf(e+".")&&delete Hl[t]}))}(o.id)})),n}})}function Lm(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=_v(),i=90===Math.abs(Number(window.orientation))?"landscape":"portrait";ow.emit("onResize",{deviceOrientation:i,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Dm(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&ow.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function zm(){const{emit:e}=ow;"visible"===document.visibilityState?e("onAppEnterForeground",c({},Bp)):e("onAppEnterBackground")}function Rm(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&jc("onTabItemTap",{index:n,text:t,pagePath:o})}function Nm(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),i=(t<10?"0":"")+t;let r=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==i&&(r=i+":"+r),r}function jm(e,t,n){const o=Wt({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),i={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];i.x=t.pageX,i.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(r){function a(){r.stopPropagation(),r.preventDefault()}n.fullscreen&&a();const s=o.gestureType;if("stop"===s)return;const l=r.targetTouches[0],c=l.pageX,u=l.pageY,d=i,f=t.value;if("progress"===s?function(e){const n=t.value.duration;let i=e/600*n+o.currentTimeOld;i<0?i=0:i>n&&(i=n);o.currentTimeNew=i}(c-d.x):"volume"===s&&function(e){const n=t.value,i=o.volumeOld;let r;"number"==typeof i&&(r=i-e/200,r<0?r=0:r>1&&(r=1),n.volume=r,o.volumeNew=r)}(u-d.y),"none"===s)if(Math.abs(c-d.x)>Math.abs(u-d.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=f.currentTime,n.fullscreen||a()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=f.volume,n.fullscreen||a()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}const Fm=vu({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const i=sn(null),r=sn(null),a=wu(i,t),{state:s}=hh(),{$attrs:l}=Ch({excludeListeners:!0}),{t:c}=Bl();jl();const{videoRef:u,state:d,play:f,pause:h,stop:g,seek:m,playbackRate:v,toggle:y,onDurationChange:b,onLoadedMetadata:w,onProgress:x,onWaiting:_,onVideoError:A,onPlay:S,onPause:T,onEnded:C,onTimeUpdate:E}=function(e,t,n){const o=sn(null),i=Er((()=>Lu(e.src))),r=Er((()=>"true"===e.muted||!0===e.muted)),a=Wt({start:!1,src:i,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:r});function s(e){const t=e.target,n=t.buffered;n.length&&(a.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return oo((()=>i.value),(()=>{a.playing=!1,a.currentTime=0})),oo((()=>a.buffered),(e=>{n("progress",{},{buffered:e})})),oo((()=>r.value),(e=>{o.value.muted=e})),{videoRef:o,state:a,play:function(){const e=o.value;a.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;a.playing?e.pause():e.play()},onDurationChange:function({target:e}){a.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,i=t.target;o>0&&(i.currentTime=o),n("loadedmetadata",t,{width:i.videoWidth,height:i.videoHeight,duration:i.duration}),s(t)},onProgress:s,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){a.playing=!1,n("error",e,{})},onPlay:function(e){a.start=!0,a.playing=!0,n("play",e,{})},onPause:function(e){a.playing=!1,n("pause",e,{})},onEnded:function(e){a.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=a.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,a),{state:k,danmuRef:P,updateDanmu:B,toggleDanmu:M,sendDanmu:I}=function(e,t){const n=sn(null),o=Wt({enable:Boolean(e.enableDanmu)});let i={time:0,index:-1};const r=p(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function a(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return r.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,s=i,l={time:n,index:s.index};if(n>s.time)for(let i=s.index+1;i<r.length;i++){const e=r[i];if(!(n>=(e.time||0)))break;l.index=i,t.playing&&o.enable&&a(e)}else if(n<s.time)for(let t=s.index-1;t>-1&&n<=(r[t].time||0);t--)l.index=t-1;i=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){r.splice(i.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,d),{state:O,onFullscreenChange:L,emitFullscreenChange:D,toggleFullscreen:z,requestFullScreen:R,exitFullScreen:N}=function(e,t,n,o,i){const r=Wt({fullscreen:!1}),a=/^Apple/.test(navigator.vendor);function s(t){r.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const r=i.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||a&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),r.appendChild(l)),u&&s(e)}function c(){l(!1)}return Vo(c),{state:r,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||s(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:s,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(a,r,u,s,i),{state:j,onTouchstart:F,onTouchend:q,onTouchmove:V}=jm(e,u,O),{state:H,progressRef:Q,ballRef:U,clickProgress:W,toggleControls:$}=function(e,t,n){const o=sn(null),i=sn(null),r=Er((()=>e.showCenterPlayBtn&&!t.start)),a=sn(!0),s=Er((()=>!r.value&&e.controls&&a.value)),l=Wt({touching:!1,controlsTouching:!1,centerPlayBtnShow:r,controlsShow:s,controlsVisible:a});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function d(){c&&(clearTimeout(c),c=null)}return Vo((()=>{c&&clearTimeout(c)})),oo((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():d()})),oo([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),jo((()=>{const e=we(!1);let r,a,s,c=!0;const u=i.value;function d(e){const n=e.targetTouches[0],i=n.pageX,l=n.pageY;if(c&&Math.abs(i-r)<Math.abs(l-a))return void f(e);c=!1;const u=o.value.offsetWidth;let d=s+(i-r)/u*100;d<0?d=0:d>100&&(d=100),t.progress=d,e.preventDefault(),e.stopPropagation()}function f(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",d,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];r=o.pageX,a=o.pageY,s=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",d,e)})),u.addEventListener("touchend",f),u.addEventListener("touchcancel",f)})),{state:l,progressRef:o,ballRef:i,clickProgress:function(e){const i=o.value;let r=e.target,a=e.offsetX;for(;r&&r!==i;)a+=r.offsetLeft,r=r.parentNode;const s=i.offsetWidth;let l=0;a>=0&&a<=s&&(l=a/s,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:d}}(e,d,m);return function(e,t,n,o,i,r,a,s){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:i,playbackRate:r,requestFullScreen:a,exitFullScreen:s};Ig(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)}),Lg(),!0)}(f,h,g,m,I,v,R,N),()=>rr("uni-video",{ref:i,id:e.id,onClick:$},[rr("div",{ref:r,class:"uni-video-container",onTouchstart:F,onTouchend:q,onTouchmove:V,onFullscreenchange:Ba(L,["stop"]),onWebkitfullscreenchange:Ba((e=>L(e,!0)),["stop"])},[rr("video",fr({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:d.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:b,onLoadedmetadata:w,onProgress:x,onWaiting:_,onError:A,onPlay:S,onPause:T,onEnded:C,onTimeupdate:e=>{E(e),B(e)},onWebkitbeginfullscreen:()=>D(!0),onX5videoenterfullscreen:()=>D(!0),onWebkitendfullscreen:()=>D(!1),onX5videoexitfullscreen:()=>D(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),Xo(rr("div",{class:"uni-video-bar uni-video-bar-full",onClick:Ba((()=>{}),["stop"])},[rr("div",{class:"uni-video-controls"},[Xo(rr("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!d.playing,"uni-video-control-button-pause":d.playing},onClick:Ba(y,["stop"])},null,10,["onClick"]),[[Ma,e.showPlayBtn]]),Xo(rr("div",{class:"uni-video-current-time"},[Nm(d.currentTime)],512),[[Ma,e.showProgress]]),Xo(rr("div",{ref:Q,class:"uni-video-progress-container",onClick:Ba(W,["stop"])},[rr("div",{class:"uni-video-progress"},[rr("div",{style:{width:d.buffered+"%"},class:"uni-video-progress-buffered"},null,4),rr("div",{ref:U,style:{left:d.progress+"%"},class:"uni-video-ball"},[rr("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[Ma,e.showProgress]]),Xo(rr("div",{class:"uni-video-duration"},[Nm(Number(e.duration)||d.duration)],512),[[Ma,e.showProgress]])]),Xo(rr("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":k.enable},onClick:Ba(M,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[Ma,e.danmuBtn]]),Xo(rr("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":O.fullscreen},onClick:Ba((()=>z(!O.fullscreen)),["stop"])},null,10,["onClick"]),[[Ma,e.showFullscreenBtn]])],8,["onClick"]),[[Ma,H.controlsShow]]),Xo(rr("div",{ref:P,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[Ma,d.start&&k.enable]]),H.centerPlayBtnShow&&rr("div",{class:"uni-video-cover",onClick:Ba((()=>{}),["stop"])},[rr("div",{class:"uni-video-cover-play-button",onClick:Ba(f,["stop"])},null,8,["onClick"]),rr("p",{class:"uni-video-cover-duration"},[Nm(Number(e.duration)||d.duration)])],8,["onClick"]),rr("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===j.gestureType}},[rr("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),rr("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[rr("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),rr("div",{class:"uni-video-toast-value"},[rr("div",{style:{width:100*j.volumeNew+"%"},class:"uni-video-toast-value-content"},[rr("div",{class:"uni-video-toast-volume-grids"},[ti(10,(()=>rr("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),rr("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===j.gestureType}},[rr("div",{class:"uni-video-toast-title"},[Nm(j.currentTimeNew)," / ",Nm(d.duration)])],2),rr("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}}),qm=({name:e,arg:t})=>{"postMessage"===e||uni[e](t)},Vm=he((()=>ow.on("onWebInvokeAppService",qm))),Hm=vu({inheritAttrs:!1,name:"WebView",props:{src:{type:String,default:""},fullscreen:{type:Boolean,default:!0}},setup(e){Vm();const t=sn(null),n=sn(null),{$attrs:o,$excludeAttrs:i,$listeners:r}=Ch({excludeListeners:!0});let a;return(()=>{const i=document.createElement("iframe");to((()=>{for(const e in o.value)if(f(o.value,e)){const t=o.value[e];i[e]=t}})),to((()=>{i.src=Lu(e.src)})),n.value=i,a=function(e,t,n){return()=>{var o,i;if(n){const{top:n,left:o,width:i,height:r}=e.value.getBoundingClientRect();pe(t.value,{position:"absolute",display:"block",border:"0",top:n+"px",left:o+"px",width:i+"px",height:r+"px"})}else pe(t.value,{width:(null==(o=e.value)?void 0:o.style.width)||"300px",height:(null==(i=e.value)?void 0:i.style.height)||"150px"})}}(t,n,e.fullscreen),e.fullscreen&&document.body.appendChild(i)})(),jo((()=>{var o;a(),!e.fullscreen&&(null==(o=t.value)||o.appendChild(n.value))})),Eo((()=>{e.fullscreen&&(n.value.style.display="block")})),ko((()=>{e.fullscreen&&(n.value.style.display="none")})),Vo((()=>{e.fullscreen&&document.body.removeChild(n.value)})),()=>rr(qi,null,[rr("uni-web-view",fr({class:e.fullscreen?"uni-webview--fullscreen":""},r.value,i.value,{ref:t}),[rr(Mp,{onResize:a},null,8,["onResize"])],16)])}});let Qm,Um=0;function Wm(e,t,n,o){var i,r=document.createElement("script"),a=t.callback||"callback",s="__uni_jsonp_callback_"+Um++,l=t.timeout||3e4;function c(){clearTimeout(i),delete window[s],r.remove()}window[s]=e=>{v(n)&&n(e),c()},r.onerror=()=>{v(o)&&o(),c()},i=setTimeout((function(){v(o)&&o(),c()}),l),r.src=e+(e.indexOf("?")>=0?"&":"?")+a+"="+s,document.body.appendChild(r)}function $m(e){function t(){const e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){const e=this.div.parentNode;e&&e.removeChild(this.div)}function o(){const t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":`${t.bgColor||"#fff"} transparent transparent`,"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position});(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function i(){}function r(){this.Text&&this.option.map.remove(this.Text)}function a(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(e={},s){this.createAMapText=o,this.removeAMapText=r,this.createBMapText=i,this.removeBMapText=a,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};const l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(tv())this.callback=s,this.visible&&this.createAMapText();else if(nv())this.visible&&this.createBMapText();else{const t=e.map;this.position=e.position,this.index=1;const n=this.div=document.createElement("div"),o=n.style;o.position="absolute",o.whiteSpace="nowrap",o.transform="translateX(-50%) translateY(-100%)",o.zIndex="1",o.boxShadow=e.boxShadow||"none",o.display=l?"block":"none";const i=this.triangle=document.createElement("div");i.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),n.appendChild(i),t&&this.setMap(t)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,tv()?this.visible&&this.createAMapText():nv()?this.visible&&this.createBMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){const t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor=`${e.bgColor||"#fff"} transparent transparent`}setPosition(e){this.position=e,this.draw()}draw(){const e=this.getProjection();if(!this.position||!this.div||!e)return;const t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}changed(){this.div.style.display=this.visible?"block":"none"}}if(!tv()&&!nv()){const t=new(e.OverlayView||e.Overlay);s.prototype.setMap=t.setMap,s.prototype.getMap=t.getMap,s.prototype.getPanes=t.getPanes,s.prototype.getProjection=t.getProjection,s.prototype.map_changed=t.map_changed,s.prototype.set=t.set,s.prototype.get=t.get,s.prototype.setOptions=t.setValues,s.prototype.bindTo=t.bindTo,s.prototype.bindsTo=t.bindsTo,s.prototype.notify=t.notify,s.prototype.setValues=t.setValues,s.prototype.unbind=t.unbind,s.prototype.unbindAll=t.unbindAll,s.prototype.addListener=t.addListener}return s}const Xm={};function Ym(e,t){const n=Km();if(!n.key)return;const o=Xm[n.type]=Xm[n.type]||[];if(Qm)t(Qm);else if(window[n.type]&&window[n.type].maps)Qm=tv()||nv()?window[n.type]:window[n.type].maps,Qm.Callout=Qm.Callout||$m(Qm),t(Qm);else if(o.length)o.push(t);else{o.push(t);const i=window,r="__map_callback__"+n.type;i[r]=function(){delete i[r],Qm=tv()||nv()?window[n.type]:window[n.type].maps,Qm.Callout=$m(Qm),o.forEach((e=>e(Qm))),o.length=0},tv()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);const a=document.createElement("script");let s=Jm(n.type);n.type===Gm.QQ&&e.push("geometry"),e.length&&(s+=`libraries=${e.join("%2C")}&`),n.type===Gm.BMAP?a.src=`${s}ak=${n.key}&callback=${r}`:a.src=`${s}key=${n.key}&callback=${r}`,a.onerror=function(){},document.body.appendChild(a)}}const Jm=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);var Gm=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(Gm||{});function Km(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let Zm=!1,ev=!1;const tv=()=>ev?Zm:(ev=!0,Zm="AMap"===Km().type),nv=()=>"BMapGL"===Km().type;const ov=yu({name:"MapMarker",props:{id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}},setup(e){const t=String(isNaN(Number(e.id))?"":e.id),n=eo("onMapReady"),o=function(e){const t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),Ho((()=>{n.remove()})),function(e){const o=Object.assign({},e,{position:"absolute",top:"70px",borderStyle:"solid"}),i=document.createElement("div");return Object.keys(o).forEach((e=>{i.style[e]=o[e]||""})),n.innerText=`.${t}{${i.getAttribute("style")}}`,t}}(t);let i;function r(e){tv()?e.removeAMapText():e.setMap(null)}if(n(((n,a,s)=>{function l(e){const l=e.title;let c;c=tv()?new a.LngLat(e.longitude,e.latitude):nv()?new a.Point(e.longitude,e.latitude):new a.LatLng(e.latitude,e.longitude);const u=new Image;let d=0;u.onload=()=>{const f=e.anchor||{};let p,h,g,m,v="number"==typeof f.x?f.x:.5,y="number"==typeof f.y?f.y:1;e.iconPath&&(e.width||e.height)?(h=e.width||u.width/u.height*e.height,g=e.height||u.height/u.width*e.width):(h=u.width/2,g=u.height/2),d=g,m=g-(g-y*g),p="MarkerImage"in a?new a.MarkerImage(u.src,null,null,new a.Point(v*h,y*g),new a.Size(h,g)):"Icon"in a?new a.Icon({image:u.src,size:new a.Size(h,g),imageSize:new a.Size(h,g),imageOffset:new a.Pixel(v*h,y*g)}):{url:u.src,anchor:new a.Point(v,y),size:new a.Size(h,g)},nv()?(i=new a.Marker(new a.Point(c.lng,c.lat)),n.addOverlay(i)):(i.setPosition(c),i.setIcon(p)),"setRotation"in i&&i.setRotation(e.rotate||0);const b=e.label||{};let w;if("label"in i&&(i.label.setMap(null),delete i.label),b.content){const e={borderColor:b.borderColor,borderWidth:(Number(b.borderWidth)||0)+"px",padding:(Number(b.padding)||0)+"px",borderRadius:(Number(b.borderRadius)||0)+"px",backgroundColor:b.bgColor,color:b.color,fontSize:(b.fontSize||14)+"px",lineHeight:(b.fontSize||14)+"px",marginLeft:(Number(b.anchorX||b.x)||0)+"px",marginTop:(Number(b.anchorY||b.y)||0)+"px"};if("Label"in a)w=new a.Label({position:c,map:n,clickable:!1,content:b.content,style:e}),i.label=w;else if("setLabel"in i)if(tv()){const t=`<div style="\n                  margin-left:${e.marginLeft};\n                  margin-top:${e.marginTop};\n                  padding:${e.padding};\n                  background-color:${e.backgroundColor};\n                  border-radius:${e.borderRadius};\n                  line-height:${e.lineHeight};\n                  color:${e.color};\n                  font-size:${e.fontSize};\n\n                  ">\n                  ${b.content}\n                <div>`;i.setLabel({content:t,direction:"bottom-right"})}else{const t=o(e);i.setLabel({text:b.content,color:e.color,fontSize:e.fontSize,className:t})}}const x=e.callout||{};let _,A=i.callout;if(x.content||l){tv()&&x.content&&(x.content=x.content.replaceAll("\n","<br/>"));const o="0px 0px 3px 1px rgba(0,0,0,0.5)";let r=-d/2;if((e.width||e.height)&&(r+=14-d/2),_=x.content?{position:c,map:n,top:m,offsetY:r,content:x.content,color:x.color,fontSize:x.fontSize,borderRadius:x.borderRadius,bgColor:x.bgColor,padding:x.padding,boxShadow:x.boxShadow||o,display:x.display}:{position:c,map:n,top:m,offsetY:r,content:l,boxShadow:o},A)A.setOption(_);else if(tv()){const e=e=>{""!==e&&s("callouttap",{},{markerId:Number(e)})};A=i.callout=new a.Callout(_,e)}else A=i.callout=new a.Callout(_),A.div.onclick=function(e){""!==t&&s("callouttap",e,{markerId:Number(t)}),e.stopPropagation(),e.preventDefault()},Km().type===Gm.GOOGLE&&(A.div.ontouchstart=function(e){e.stopPropagation()},A.div.onpointerdown=function(e){e.stopPropagation()})}else A&&(r(A),delete i.callout)},e.iconPath&&(u.src=Lu(e.iconPath))}!function(e){nv()||(i=new a.Marker({map:n,flat:!0,autoRotation:!1})),l(e);const o=a.event||a.Event;nv()||o.addListener(i,"click",(()=>{const n=i.callout;if(n&&!n.alwaysVisible)if(tv())n.visible=!n.visible,n.visible?i.callout.createAMapText():i.callout.removeAMapText();else if(n.set("visible",!n.visible),n.visible){const e=n.div,t=e.parentNode;t.removeChild(e),t.appendChild(e)}t&&s("markertap",{},{markerId:Number(t),latitude:e.latitude,longitude:e.longitude})}))}(e),oo(e,l)})),t){const e=eo("addMapChidlContext"),o=eo("removeMapChidlContext"),r={id:t,translate(e){n(((t,n,o)=>{const r=e.destination,a=e.duration,s=!!e.autoRotate;let l=Number(e.rotate)||0,c=0;"getRotation"in i&&(c=i.getRotation());const u=i.getPosition(),d=new n.LatLng(r.latitude,r.longitude),f=n.geometry.spherical.computeDistanceBetween(u,d)/1e3/(("number"==typeof a?a:1e3)/36e5),p=n.event||n.Event,h=p.addListener(i,"moving",(e=>{const t=e.latLng,n=i.label;n&&n.setPosition(t);const o=i.callout;o&&o.setPosition(t)})),g=p.addListener(i,"moveend",(()=>{g.remove(),h.remove(),i.lastPosition=u,i.setPosition(d);const t=i.label;t&&t.setPosition(d);const n=i.callout;n&&n.setPosition(d);const o=e.animationEnd;v(o)&&o()}));let m=0;s&&(i.lastPosition&&(m=n.geometry.spherical.computeHeading(i.lastPosition,u)),l=n.geometry.spherical.computeHeading(u,d)-m),"setRotation"in i&&i.setRotation(c+l),"moveTo"in i?i.moveTo(d,f):(i.setPosition(d),p.trigger(i,"moveend",{}))}))}};e(r),Ho((()=>o(r)))}return Ho((function(){i&&(i.label&&"setMap"in i.label&&i.label.setMap(null),i.callout&&r(i.callout),i.setMap(null))})),()=>null}});function iv(e){if(!e)return{r:0,g:0,b:0,a:0};let t=e.slice(1);const n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};3!==n&&4!==n||(t=t.replace(/(\w{1})/g,"$1$1"));let[o,i,r,a]=t.match(/(\w{2})/g);const s=parseInt(o,16),l=parseInt(i,16),c=parseInt(r,16);return a?{r:s,g:l,b:c,a:(`0x100${a}`-65536)/255}:{r:s,g:l,b:c,a:1}}const rv={points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default:()=>[]},level:{type:String,default:""}},av=yu({name:"MapPolyline",props:rv,setup(e){let t,n;function o(){t&&t.setMap(null),n&&n.setMap(null)}return eo("onMapReady")(((i,r)=>{function a(e){const o=[];e.points.forEach((e=>{let t;t=tv()?[e.longitude,e.latitude]:nv()?new r.Point(e.longitude,e.latitude):new r.LatLng(e.latitude,e.longitude),o.push(t)}));const a=Number(e.width)||1,{r:s,g:l,b:c,a:u}=iv(e.color),{r:d,g:f,b:p,a:h}=iv(e.borderColor),g={map:i,clickable:!1,path:o,strokeWeight:a,strokeColor:e.color||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"},m=Number(e.borderWidth)||0,v={map:i,clickable:!1,path:o,strokeWeight:a+2*m,strokeColor:e.borderColor||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"};"Color"in r?(g.strokeColor=new r.Color(s,l,c,u),v.strokeColor=new r.Color(d,f,p,h)):(g.strokeColor=`rgb(${s}, ${l}, ${c})`,g.strokeOpacity=u,v.strokeColor=`rgb(${d}, ${f}, ${p})`,v.strokeOpacity=h),m&&(n=new r.Polyline(v)),nv()?(t=new r.Polyline(g.path,g),i.addOverlay(t)):t=new r.Polyline(g)}a(e),oo(e,(function(e){o(),a(e)}))})),Ho(o),()=>null}}),sv=yu({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){let t;function n(){t&&t.setMap(null)}return eo("onMapReady")(((o,i)=>{function r(e){const n=tv()||nv()?[e.longitude,e.latitude]:new i.LatLng(e.latitude,e.longitude),r={map:o,center:n,clickable:!1,radius:e.radius,strokeWeight:Number(e.strokeWidth)||1,strokeDashStyle:"solid"};if(nv())r.strokeColor=e.color,r.fillColor=e.fillColor||"#000",r.fillOpacity=1;else{const{r:t,g:n,b:o,a:a}=iv(e.fillColor),{r:s,g:l,b:c,a:u}=iv(e.color);"Color"in i?(r.fillColor=new i.Color(t,n,o,a),r.strokeColor=new i.Color(s,l,c,u)):(r.fillColor=`rgb(${t}, ${n}, ${o})`,r.fillOpacity=a,r.strokeColor=`rgb(${s}, ${l}, ${c})`,r.strokeOpacity=u)}if(nv()){let e=new i.Point(r.center[0],r.center[1]);t=new i.Circle(e,r.radius,r),o.addOverlay(t)}else t=new i.Circle(r),tv()&&o.add(t)}r(e),oo(e,(function(e){n(),r(e)}))})),Ho(n),()=>null}}),lv={id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}},cv=yu({name:"MapControl",props:lv,setup(e){const t=Er((()=>Lu(e.iconPath))),n=Er((()=>{let t=`top:${e.position.top||0}px;left:${e.position.left||0}px;`;return e.position.width&&(t+=`width:${e.position.width}px;`),e.position.height&&(t+=`height:${e.position.height}px;`),t})),o=t=>{e.clickable&&e.trigger("controltap",t,{controlId:e.id})};return()=>rr("div",{class:"uni-map-control"},[rr("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:o},null,12,["src","onClick"])])}}),uv=Od("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),dv=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let fv;function pv(){if(fv=fv||dv.__DC_STAT_UUID,!fv){fv=Date.now()+""+Math.floor(1e7*Math.random());try{dv.__DC_STAT_UUID=fv}catch(e){}}return fv}function hv(){if(!0!==__uniConfig.darkmode)return y(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function gv(){let e,t="0",n="",o="phone";const i=navigator.language;if(Ru){e="iOS";const o=Du.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const i=Du.match(/\(([a-zA-Z]+);/);i&&(n=i[1])}else if(zu){e="Android";const o=Du.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const i=Du.match(/\((.+?)\)/),r=i?i[1].split(";"):Du.split(" "),a=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<r.length;e++){const t=r[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<a.length;e++)if(a[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(qu)n="iPad",e="iOS",o="pad",t=v(window.BigInt)?"14.0":"13.0";else if(Nu||ju||Fu){n="PC",e="PC",o="pc",t="0";let i=Du.match(/\((.+?)\)/)[1];if(Nu){switch(e="Windows",Nu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=i&&i.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(ju){e="macOS";const n=i&&i.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Fu){e="Linux";const n=i&&i.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const r=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)s="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],i=new RegExp(`(${o})/(\\S*)\\b`);i.test(Du)&&(s=t[n],l=Du.match(i)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:r,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:i,deviceType:o,ua:Du,osname:e,osversion:t,theme:hv()}}const mv=Id(0,(()=>{const e=window.devicePixelRatio,t=Vu(),n=Hu(t),o=Qu(t,n),i=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),r=Uu(o);let a=window.innerHeight;const s=hc.top,l={left:hc.left,right:r-hc.right,top:hc.top,bottom:a-hc.bottom,width:r-hc.left-hc.right,height:a-hc.top-hc.bottom},{top:c,bottom:u}=bc();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:r,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:i,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:hc.top,right:hc.right,bottom:hc.bottom,left:hc.left},screenTop:i-a}}));let vv,yv=!0;function bv(){yv&&(vv=gv())}const wv=Id(0,(()=>{bv();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:i,system:r,deviceOrientation:a,deviceType:s}=vv;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:pv(),deviceOrientation:a,deviceType:s,model:o,platform:i,system:r}})),xv=Id(0,(()=>{bv();const{theme:e,language:t,browserName:n,browserVersion:o}=vv;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Mf?Mf():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),_v=Id(0,(()=>{yv=!0,bv(),yv=!1;const e=mv(),t=wv(),n=xv();yv=!0;const{ua:o,browserName:i,browserVersion:r,osname:a,osversion:s}=vv,l=c(e,t,n,{ua:o,browserName:i,browserVersion:r,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),Av=Od("getSystemInfo",((e,{resolve:t})=>t(_v())));function Sv(){kv().then((({networkType:e})=>{ow.invokeOnCallback("onNetworkStatusChange",{isConnected:"none"!==e,networkType:e})}))}function Tv(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const Cv=Pd("onNetworkStatusChange",(()=>{const e=Tv();e?e.addEventListener("change",Sv):(window.addEventListener("offline",Sv),window.addEventListener("online",Sv))})),Ev=Bd("offNetworkStatusChange",(()=>{const e=Tv();e?e.removeEventListener("change",Sv):(window.removeEventListener("offline",Sv),window.removeEventListener("online",Sv))})),kv=Od("getNetworkType",((e,{resolve:t})=>{const n=Tv();let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})}));let Pv=null;const Bv=Pd("onCompass",(()=>{Iv()})),Mv=Bd("offCompass",(()=>{Ov()})),Iv=Od("startCompass",((e,{resolve:t,reject:n})=>{if(window.DeviceOrientationEvent){if(!Pv){if(DeviceOrientationEvent.requestPermission)return void DeviceOrientationEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){Pv=function(e){const t=360-(null!==e.alpha?e.alpha:360);ow.invokeOnCallback("onCompass",{direction:t})},window.addEventListener("deviceorientation",Pv,!1)}})),Ov=Od("stopCompass",((e,{resolve:t})=>{Pv&&(window.removeEventListener("deviceorientation",Pv,!1),Pv=null),t()}));const Lv=Od("setClipboardData",((e,t)=>{return n=void 0,o=[e,t],i=function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const i=document.createElement("textarea");i.id="#clipboard",i.style.position="fixed",i.style.top="-9999px",i.style.zIndex="-9999",document.body.appendChild(i),i.value=e,i.select(),i.setSelectionRange(0,i.value.length);const r=document.execCommand("Copy",!1);i.blur(),r?t():n()}(e,t,n)}},new Promise(((e,t)=>{var r=e=>{try{s(i.next(e))}catch(n){t(n)}},a=e=>{try{s(i.throw(e))}catch(n){t(n)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(r,a);s((i=i.apply(n,o)).next())}));var n,o,i}),0,qf);const Dv=Id(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),zv=Od("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{Dv(e,t),n()}catch(i){o(i.message)}}));function Rv(e){const t=localStorage&&localStorage.getItem(e);if(!y(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=y(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Nv=Id(0,(e=>{try{return Rv(e)}catch(t){return""}})),jv=Od("getStorage",(({key:e},{resolve:t,reject:n})=>{try{t({data:Rv(e)})}catch(o){n(o.message)}})),Fv=Id(0,(e=>{localStorage&&localStorage.removeItem(e)})),qv=Od("removeStorage",(({key:e},{resolve:t})=>{Fv(e),t()})),Vv=Id(0,(()=>{localStorage&&localStorage.clear()})),Hv=Od("clearStorage",((e,{resolve:t})=>{Vv(),t()})),Qv=Od("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())})),Uv={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Wv({count:e,sourceType:t,type:n,extension:o}){const i=document.createElement("input");return i.type="file",pe(i,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),i.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Uv[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(i.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&i.setAttribute("capture","camera"),i}fh();let $v=null;const Xv=Od("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:i,reject:r})=>{Rl();const{t:a}=Bl();$v&&(document.body.removeChild($v),$v=null),$v=Wv({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild($v),$v.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let i=0;i<t;i++){const t=n.files[i];let r;Object.defineProperty(t,"path",{get:()=>(r=r||Ep(t),r)}),i<e&&o.push(t)}}i({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),$v.click(),ph()}),0,$f);let Yv=null;const Jv=Od("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:i})=>{Rl();const{t:r}=Bl();Yv&&(document.body.removeChild(Yv),Yv=null),Yv=Wv({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Yv),Yv.addEventListener("change",(function(t){const n=t.target,i=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let r;Object.defineProperty(t,"path",{get:()=>(r=r||Ep(t),r)}),o<e&&i.push(t)}}o({get tempFilePaths(){return i.map((({path:e})=>e))},tempFiles:i})})),Yv.click(),ph()}),0,Qf),Gv={esc:["Esc","Escape"],enter:["Enter"]},Kv=Object.keys(Gv);function Zv(){const e=sn(""),t=sn(!1),n=n=>{if(t.value)return;const o=Kv.find((e=>-1!==Gv[e].indexOf(n.key)));o&&(e.value=o),In((()=>e.value=""))};return jo((()=>{document.addEventListener("keyup",n)})),Vo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const ey=rr("div",{class:"uni-mask"},null,-1);function ty(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Da(yo({setup:()=>()=>($i(),Ki(e,t,null,16))}))}function ny(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function oy(e,{onEsc:t,onEnter:n}){const o=sn(e.visible),{key:i,disable:r}=Zv();return oo((()=>e.visible),(e=>o.value=e)),oo((()=>o.value),(e=>r.value=!e)),to((()=>{const{value:e}=i;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let iy=0,ry="";function ay(e){let t=iy;iy+=e?1:-1,iy=Math.max(0,iy),iy>0?0===t&&(ry=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=ry,ry="")}function sy(){jo((()=>ay(!0))),Ho((()=>ay(!1)))}const ly=yu({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Wt({direction:"none"});let n=1,o=0,i=0,r=0,a=0;function s({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,i=t.height}function c(e){const t=e.target.getBoundingClientRect();r=t.width,a=t.height,d(e)}function u(e){const s=n*o>r,l=n*i>a;t.direction=s&&l?"all":s?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return rr(kh,{style:n,onTouchstart:bu(c),onTouchmove:bu(d),onTouchend:bu(u)},{default:()=>[rr(qh,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[rr("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function cy(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const uy=yu({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){sy();const n=sn(null),o=sn(cy(e));let i;function r(){i||In((()=>{t("close")}))}function a(e){o.value=e.detail.current}oo((()=>e.current),(()=>o.value=cy(e))),jo((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{i=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(i=!0)}))}));const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return rr("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:r},[rr(vg,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(i=t=e.urls.map((e=>rr(bg,null,{default:()=>[rr(ly,{src:e},null,8,["src"])]}))),"function"==typeof i||"[object Object]"===Object.prototype.toString.call(i)&&!Zi(i)?t:{default:()=>[t],_:1}),8,["current","onChange"]),rr("div",{style:s},[Bc(kc,"#ffffff",26)],4)],8,["onClick"]);var i}}});let dy,fy=null;const py=()=>{fy=null,In((()=>{null==dy||dy.unmount(),dy=null}))},hy=Od("previewImage",((e,{resolve:t})=>{fy?c(fy,e):(fy=Wt(e),In((()=>{dy=ty(uy,fy,py),dy.mount(ny("u-a-p"))}))),t()}),0,Xf);let gy=null;const my=Od("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{Rl();const{t:i}=Bl();gy&&(document.body.removeChild(gy),gy=null),gy=Wv({sourceType:e,extension:t,type:"video"}),document.body.appendChild(gy),gy.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const i={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(i,"tempFilePath",{get(){return o=o||Ep(this.tempFile),o}});const r=document.createElement("video");if(void 0!==r.onloadedmetadata){const e=Ep(t);r.onloadedmetadata=function(){kp(e),n(c(i,{duration:r.duration||0,width:r.videoWidth||0,height:r.videoHeight||0}))},setTimeout((()=>{r.onloadedmetadata=null,kp(e),n(i)}),300),r.src=e}else n(i)})),gy.click(),ph()}),0,Uf),vy=Md("request",(({url:e,data:t,header:n={},method:o,dataType:i,responseType:r,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(y(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)f(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const p=new XMLHttpRequest,h=new yy(p);p.open(o,e);for(const v in n)f(n,v)&&p.setRequestHeader(v,n[v]);const g=setTimeout((function(){p.onload=p.onabort=p.onerror=null,h.abort(),c("timeout",{errCode:5})}),s);return p.responseType=r,p.onload=function(){clearTimeout(g);const e=p.status;let t="text"===r?p.responseText:p.response;if("text"===r&&"json"===i)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:by(p.getAllResponseHeaders()),cookies:[]})},p.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},p.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},p.withCredentials=a,p.send(u),h}),0,Kf);class yy{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function by(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class wy{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){v(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const xy=Md("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:i})=>{var r,a=new XMLHttpRequest,s=new wy(a);return a.open("GET",e,!0),Object.keys(t).forEach((e=>{a.setRequestHeader(e,t[e])})),a.responseType="blob",a.onload=function(){clearTimeout(r);const t=a.status,n=this.response;let i;const s=a.getResponseHeader("content-disposition");if(s){const e=s.match(/filename="?(\S+)"?\b/);e&&(i=e[1])}n.name=i||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:Ep(n)})},a.onabort=function(){clearTimeout(r),i("abort",{errCode:600003})},a.onerror=function(){clearTimeout(r),i("",{errCode:602001})},a.onprogress=function(e){s._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},a.send(),r=setTimeout((function(){a.onprogress=a.onload=a.onabort=a.onerror=null,s.abort(),i("timeout",{errCode:5})}),n),s}),0,Zf);class _y{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){v(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Ay=Md("uploadFile",(({url:e,file:t,filePath:n,name:o,files:i,header:r={},formData:a={},timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new _y;return p(i)&&i.length||(i=[{name:o,file:t,uri:n}]),Promise.all(i.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Cp(e)):Tp(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(a).forEach((e=>{d.append(e,a[e])})),Object.values(i).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(r).forEach((e=>{o.setRequestHeader(e,r[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),s),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,ep),Sy=[],Ty={open:"",close:"",error:"",message:""};class Cy{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:i}=t,r="message"===e?{data:n}:"close"===e?{code:o,reason:i}:{};if(this._callbacks[e].forEach((e=>{try{e(r)}catch(t){}})),this===Sy[0]&&Ty[e]&&ow.invokeOnCallback(Ty[e],r),"error"===e||"close"===e){const e=Sy.indexOf(this);e>=0&&Sy.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(i){o=i}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw ge(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),ge(e,"sendSocketMessage:ok")}catch(o){ge(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;y(o)?t.close(n,o):t.close(n),ge(e,"closeSocket:ok")}catch(n){ge(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const Ey=Md("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new Cy(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(Sy.push(t),n())}))),0,tp);function ky(e,t,n,o,i){const r=e[t];v(r)&&r.call(e,c({},n,{success(){o()},fail({errMsg:e}){i(e.replace("sendSocketMessage:fail ",""))},complete:void 0}))}const Py=Od("sendSocketMessage",((e,{resolve:t,reject:n})=>{const o=Sy[0];o&&o.readyState===o.OPEN?ky(o,"send",e,t,n):n("WebSocket is not connected")})),By=Od("closeSocket",((e,{resolve:t,reject:n})=>{const o=Sy[0];o?ky(o,"close",e,t,n):n("WebSocket is not connected")}));function My(e){const t=`onSocket${I(e)}`;return Pd(t,(()=>{Ty[e]=t}))}const Iy=My("open"),Oy=My("error"),Ly=My("message"),Dy=My("close"),zy=Od("getLocation",(({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:i,reject:r})=>{const a=Km();new Promise(((e,i)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>e({coords:t.coords})),i,{enableHighAccuracy:o||t,timeout:n||1e5}):i(new Error("device nonsupport geolocation"))})).catch((e=>new Promise(((t,n)=>{a.type===Gm.QQ?Wm(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${a.key}`,{callback:"callback"},(e=>{if("result"in e&&e.result.location){const n=e.result.location;t({coords:{latitude:n.lat,longitude:n.lng},skip:!0})}else n(new Error(e.message||JSON.stringify(e)))}),(()=>n(new Error("network error")))):a.type===Gm.GOOGLE?vy({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${a.key}`,success(e){const o=e.data;"location"in o?t({coords:{latitude:o.location.lat,longitude:o.location.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.error&&o.error.message||JSON.stringify(e)))},fail(){n(new Error("network error"))}}):a.type===Gm.AMAP?Ym([],(()=>{window.AMap.plugin("AMap.Geolocation",(()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition(((e,o)=>{"complete"===e?t({coords:{latitude:o.position.lat,longitude:o.position.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.message))}))}))})):n(e)})))).then((({coords:t,skip:n})=>{(function(e,t,n){const o=Km();return e&&"WGS84"===e.toUpperCase()||["google"].includes(o.type)||n?Promise.resolve(t):"qq"===o.type?new Promise((e=>{Wm(`https://apis.map.qq.com/jsapi?qt=translate&type=1&points=${t.longitude},${t.latitude}&key=${o.key}&output=jsonp&pf=jsapi&ref=jsapi`,{callback:"cb"},(n=>{if("detail"in n&&"points"in n.detail&&n.detail.points.length){const{lng:o,lat:i}=n.detail.points[0];e({longitude:o,latitude:i,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}),(()=>e(t)))})):"AMap"===o.type?new Promise((e=>{Ym([],(()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",((n,o)=>{if("ok"===o.info&&o.locations.length){const{lat:n,lng:i}=o.locations[0];e({longitude:i,latitude:n,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}))}))})):Promise.reject(new Error("translate coordinate system faild"))})(e,t,n).then((e=>{i({latitude:e.latitude,longitude:e.longitude,accuracy:e.accuracy,speed:e.altitude||0,altitude:e.altitude||0,verticalAccuracy:e.altitudeAccuracy||0,horizontalAccuracy:e.accuracy||0})})).catch((e=>{r(e.message)}))})).catch((e=>{r(e.message||JSON.stringify(e))}))}),0,Hf);const Ry=yu({name:"LoctaionPicker",props:{latitude:{type:Number},longitude:{type:Number}},emits:["close"],setup(e,{emit:t}){sy(),Fl();const{t:n}=Bl(),o=function(e){const t=Wt({latitude:0,longitude:0,keyword:"",searching:!1});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return oo([()=>e.latitude,()=>e.longitude],n),n(),t}(e),{list:i,listState:r,loadMore:a,reset:s,getList:l}=function(e){const t=__uniConfig.qqMapKey,n=Wt([]),o=sn(-1),i=Er((()=>n[o.value])),r=Wt({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:o,selected:i}),a=sn(""),s=Er((()=>a.value?`region(${a.value},1,${e.latitude},${e.longitude})`:`nearby(${e.latitude},${e.longitude},5000)`));function l(e){e.forEach((e=>{n.push({name:e.title||e.name,address:e.address,distance:e._distance||e.distance,latitude:e.location.lat,longitude:e.location.lng})}))}function c(){r.loading=!0;const o=Km();if(o.type===Gm.GOOGLE){if(r.pageIndex>1&&r.nextPage)return void r.nextPage();new google.maps.places.PlacesService(document.createElement("div"))[e.searching?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},((e,t,o)=>{r.loading=!1,e&&e.length&&e.forEach((e=>{n.push({name:e.name||"",address:e.vicinity||e.formatted_address||"",distance:0,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()})})),o&&(o.hasNextPage?r.nextPage=()=>{o.nextPage()}:r.hasNextPage=!1)}))}else o.type===Gm.QQ?Wm(e.searching?`https://apis.map.qq.com/ws/place/v1/search?output=jsonp&key=${t}&boundary=${s.value}&keyword=${e.keyword}&page_size=${r.pageSize}&page_index=${r.pageIndex}`:`https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${t}&location=${e.latitude},${e.longitude}&get_poi=1&poi_options=page_size=${r.pageSize};page_index=${r.pageIndex}`,{callback:"callback"},(t=>{if(r.loading=!1,e.searching&&"data"in t&&t.data.length)l(t.data);else if("result"in t){const e=t.result;a.value=e.ad_info?e.ad_info.adcode:"",e.pois&&l(e.pois)}n.length===r.pageSize*r.pageIndex&&(r.hasNextPage=!1)}),(()=>{r.loading=!1})):o.type===Gm.AMAP&&window.AMap.plugin("AMap.PlaceSearch",(function(){const t=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:r.pageIndex}),n=e.searching?e.keyword:"",o=e.searching?5e4:5e3;t.searchNearBy(n,[e.longitude,e.latitude],o,(function(e,t){"error"===e||("no_data"===e?r.hasNextPage=!1:l(t.poiList.pois))})),r.loading=!1}))}return{listState:r,list:n,loadMore:function(){!r.loading&&r.hasNextPage&&(r.pageIndex++,c())},reset:function(){r.selectedIndex=-1,r.pageIndex=1,r.hasNextPage=!0,r.nextPage=null,n.splice(0,n.length)},getList:c}}(o),u=Ce((()=>{s(),o.keyword&&l()}),1e3,{setTimeout:setTimeout,clearTimeout:clearTimeout});function d(e){o.keyword=e.detail.value,u()}function f(){t("close",c({},r.selected))}function p(){t("close")}function h(e){const t=e.detail.centerLocation;t&&m(t)}function g(){zy({type:"gcj02",success:m,fail:()=>{}})}function m({latitude:e,longitude:t}){o.latitude=e,o.longitude=t,o.searching||(s(),l())}return oo((()=>o.searching),(e=>{s(),e||l()})),o.latitude&&o.longitude||g(),()=>{const e=i.map(((e,t)=>{return rr("div",{key:t,class:{"list-item":!0,selected:r.selectedIndex===t},onClick:()=>{r.selectedIndex=t,o.latitude=e.latitude,o.longitude=e.longitude}},[Bc(Pc,"#007aff",24),rr("div",{class:"list-item-title"},[e.name]),rr("div",{class:"list-item-detail"},[(n=e.distance,n>100?`${n>1e3?(n/1e3).toFixed(1)+"k":n.toFixed(0)}m | `:n>0?"<100m | ":""),e.address])],10,["onClick"]);var n}));return r.loading&&e.unshift(rr("div",{class:"list-loading"},[rr("i",{class:"uni-loading"},null)])),rr("div",{class:"uni-system-choose-location"},[rr(tw,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:l,onRegionchange:h},{default:()=>[rr("div",{class:"map-location",style:'background-image: url("data:image/png;base64,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")'},null),rr("div",{class:"map-move",onClick:g},[Bc("M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z","#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),rr("div",{class:"nav"},[rr("div",{class:"nav-btn back",onClick:p},[Bc(kc,"#ffffff",26)],8,["onClick"]),rr("div",{class:{"nav-btn":!0,confirm:!0,disable:!r.selected},onClick:f},[Bc(Pc,"#ffffff",26)],10,["onClick"])]),rr("div",{class:"menu"},[rr("div",{class:"search"},[rr(Ah,{value:o.keyword,class:"search-input",placeholder:n("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:d},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&rr("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[n("uni.chooseLocation.cancel")],8,["onClick"])]),rr(pg,{"scroll-y":!0,class:"list",onScrolltolower:a},(t=e,"function"==typeof t||"[object Object]"===Object.prototype.toString.call(t)&&!Zi(t)?e:{default:()=>[e],_:2}),8,["scroll-y","onScrolltolower"])])]);var t}}});let Ny=null;const jy=Od("chooseLocation",((e,{resolve:t,reject:n})=>{Ny?n("cancel"):(Ny=Wt(e),In((()=>{const e=ty(Ry,Ny,(o=>{Ny=null,In((()=>{e.unmount()})),o?t(o):n("cancel")}));e.mount(ny("u-a-c"))})))})),Fy=Od("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===jc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(Pm().$router.go(-e.delta),t()):n("onBackPress")}),0,sp);function qy({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:i},r){const a=Pm().$router,{path:s,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Te(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++sm,__type__:e}}(e,r);a["navigateTo"===e?"push":"replace"]({path:s,query:l,state:u,force:!0}).then((r=>{if(Ts(r))return c(r.message);if("switchTab"===e&&(a.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=a.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Ee(u.__id__,o),t(i?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return i?t({__id__:u.__id__}):t()}))}))}const Vy=Od("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:i})=>qy({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(i)),0,op);const Hy=Od("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Oc();if(!e)return;const t=e.$page;am(um(t.path,t.id))}(),qy({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,ip);const Qy=Od("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=im().keys();for(const t of e)am(t)}(),qy({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,rp);function Uy(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const Wy=Od("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:i})=>(function(){const e=zc();if(!e)return;const t=im(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:am(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,jc(e,"onHide"))}(),qy({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=im().values();for(const n of t){const t=n.$page;if(Uy(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(i))),0,ap);function $y(e){__uniConfig.darkmode&&ow.on("onThemeChange",e)}function Xy(e){ow.off("onThemeChange",e)}function Yy(e){let t={};return __uniConfig.darkmode&&(t=ze(e,__uniConfig.themeConfig,hv())),__uniConfig.darkmode?t:e}function Jy(e,t){const n=Yt(e),o=n?Wt(Yy(e)):Yy(e);return __uniConfig.darkmode&&n&&oo(e,(e=>{const t=Yy(e);for(const n in t)o[n]=t[n]})),t&&$y(t),o}const Gy={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Ky=yo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=sn(""),o=()=>a.value=!1,i=()=>(o(),t("close","cancel")),r=()=>(o(),t("close","confirm",n.value)),a=oy(e,{onEsc:i,onEnter:()=>{!e.editable&&r()}}),s=function(e){const t=sn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Gy[e].cancelColor})(e,t)};return to((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===hv()&&n({theme:"dark"}),$y(n))):Xy(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,rr(oa,{name:"uni-fade"},{default:()=>[Xo(rr("uni-modal",{onTouchmove:gc},[ey,rr("div",{class:"uni-modal"},[t&&rr("div",{class:"uni-modal__hd"},[rr("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),d?rr("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):rr("div",{class:"uni-modal__bd",onTouchmovePassive:mc,textContent:o},null,40,["onTouchmovePassive","textContent"]),rr("div",{class:"uni-modal__ft"},[l&&rr("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:i},[e.cancelText],12,["onClick"]),rr("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:r},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Ma,a.value]])]})}}});let Zy;const eb=he((()=>{ow.on("onHidePopup",(()=>Zy.visible=!1))}));let tb;function nb(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&Zy.editable&&(o.content=t),tb&&tb(o)}const ob=Od("showModal",((e,{resolve:t})=>{eb(),tb=t,Zy?(c(Zy,e),Zy.visible=!0):(Zy=Wt(e),In((()=>(ty(Ky,Zy,nb).mount(ny("u-a-m")),In((()=>Zy.visible=!0))))))}),0,mp),ib={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==vp.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},rb={light:"#fff",dark:"rgba(255,255,255,0.9)"},ab=e=>rb[e],sb=yo({name:"Toast",props:ib,setup(e){Ll(),Dl();const{Icon:t}=function(e){const t=sn(ab(hv())),n=({theme:e})=>t.value=ab(e);to((()=>{e.visible?$y(n):Xy(n)}));return{Icon:Er((()=>{switch(e.icon){case"success":return rr(Bc(Tc,t.value,38),{class:"uni-toast__icon"});case"error":return rr(Bc(Cc,t.value,38),{class:"uni-toast__icon"});case"loading":return rr("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=oy(e,{});return()=>{const{mask:o,duration:i,title:r,image:a}=e;return rr(oa,{name:"uni-fade"},{default:()=>[Xo(rr("uni-toast",{"data-duration":i},[o?rr("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:gc},null,40,["onTouchmove"]):"",a||t.value?rr("div",{class:"uni-toast"},[a?rr("img",{src:a,class:"uni-toast__icon"},null,10,["src"]):t.value,rr("p",{class:"uni-toast__content"},[r])]):rr("div",{class:"uni-sample-toast"},[rr("p",{class:"uni-simple-toast__text"},[r])])],8,["data-duration"]),[[Ma,n.value]])]})}}});let lb,cb,ub="";const db=je();function fb(e){lb?c(lb,e):(lb=Wt(c(e,{visible:!1})),In((()=>{db.run((()=>{oo([()=>lb.visible,()=>lb.duration],(([e,t])=>{if(e){if(cb&&clearTimeout(cb),"onShowLoading"===ub)return;cb=setTimeout((()=>{vb("onHideToast")}),t)}else cb&&clearTimeout(cb)}))})),ow.on("onHidePopup",(()=>vb("onHidePopup"))),ty(sb,lb,(()=>{})).mount(ny("u-a-t"))}))),setTimeout((()=>{lb.visible=!0}),10)}const pb=Od("showToast",((e,{resolve:t,reject:n})=>{fb(e),ub="onShowToast",t()}),0,yp),hb={icon:"loading",duration:1e8,image:""},gb=Od("showLoading",((e,{resolve:t,reject:n})=>{c(e,hb),fb(e),ub="onShowLoading",t()}),0,gp),mb=Od("hideLoading",((e,{resolve:t,reject:n})=>{vb("onHideLoading"),t()}));function vb(e){const{t:t}=Bl();if(!ub)return;let n="";"onHideToast"===e&&"onShowToast"!==ub?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==ub&&(n=t("uni.showLoading.unpaired")),n||(ub="",setTimeout((()=>{lb.visible=!1}),10))}const yb={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const bb=yo({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){Ol();const n=sn(260),o=sn(0),i=sn(0),r=sn(0),a=sn(0),s=sn(null),l=sn(null),{t:u}=Bl(),{_close:d}=function(e,t){function n(e){t("close",e)}const{key:o,disable:i}=Zv();return oo((()=>e.visible),(e=>i.value=!e)),to((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:f}=function(e){const t=sn(0),n=sn(0),o=Er((()=>t.value>=500&&n.value>=500)),i=Er((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},i=t.content,r=t.triangle,a=e.popover;function s(e){return Number(e)||0}if(o.value&&a){c(r,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=s(a.left),t=s(a.width),o=s(a.top),l=s(a.height),u=e+t/2;i.transform="none !important";const d=Math.max(0,u-150);i.left=`${d}px`;let f=Math.max(12,u-d);f=Math.min(288,f),r.left=`${f}px`;const p=n.value/2;o+l-p>p-o?(i.top="auto",i.bottom=n.value-o+6+"px",r.bottom="-6px",r["border-width"]="6px 6px 0 6px",r["border-color"]="#fcfcfd transparent transparent transparent"):(i.top=`${o+l+6}px`,r.top="-6px",r["border-width"]="0 6px 6px 6px",r["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return jo((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:i}=_v();t.value=e,n.value=o+(i||0)};window.addEventListener("resize",e),e(),Ho((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:i}}(e);let p;function h(e){const t=r.value+e.deltaY;Math.abs(t)>10?(a.value+=t/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,p.scrollTo(a.value)):r.value=t,e.preventDefault()}jo((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new og(e,t);function i(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,i=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=i.screenX,n.y=i.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||i.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const r=i(e);if(r){for(n.maxDy=Math.max(n.maxDy,Math.abs(r.y)),n.maxDx=Math.max(n.maxDx,Math.abs(r.x)),n.historyX.push(r.x),n.historyY.push(r.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(r.x,r.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=i(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,i=n.historyTime[t],r=n.historyX[t],a=n.historyY[t];t>0;){t--;const e=i-n.historyTime[t];if(e>30&&e<50){o.x=(r-n.historyX[t])/(e/1e3),o.y=(a-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(s.value,{enableY:!0,friction:new Kh(1e-4),spring:new tg(2,90,20),onScroll:e=>{a.value=e.target.scrollTop}});p=e,Oh(s.value,(i=>{if(e)switch(i.detail.state){case"start":t(i);break;case"move":n(i);break;case"end":case"cancel":o(i)}}),!0)})),oo((()=>e.visible),(()=>{In((()=>{e.title&&(i.value=document.querySelector(".uni-actionsheet__title").offsetHeight),p.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const i=e.changedTouches[0];if(Math.abs(i.clientX-n)<t&&Math.abs(i.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=i[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const g=function(e){const t=Wt({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=yb[e][n]}))}(e,t)};return to((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:hv()}),$y(n))):Xy(n)})),t}(e);return()=>rr("uni-actionsheet",{onTouchmove:gc},[rr(oa,{name:"uni-fade"},{default:()=>[Xo(rr("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>d(-1)},null,8,["onClick"]),[[Ma,e.visible]])]}),rr("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:f.value.content},[rr("div",{ref:l,class:"uni-actionsheet__menu",onWheel:h},[e.title?rr(qi,null,[rr("div",{class:"uni-actionsheet__cell",style:{height:`${i.value}px`}},null),rr("div",{class:"uni-actionsheet__title"},[e.title])]):"",rr("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[rr("div",{ref:s},[e.itemList.map(((e,t)=>rr("div",{key:t,style:{color:g.listItemColor},class:"uni-actionsheet__cell",onClick:()=>d(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),rr("div",{class:"uni-actionsheet__action"},[rr("div",{style:{color:g.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>d(-1)},[u("uni.showActionSheet.cancel")],12,["onClick"])]),rr("div",{style:f.value.triangle},null,4)],6)],40,["onTouchmove"])}});let wb,xb,_b;const Ab=he((()=>{ow.on("onHidePopup",(()=>_b.visible=!1))}));function Sb(e){-1===e?xb&&xb("cancel"):wb&&wb({tapIndex:e})}const Tb=Od("showActionSheet",((e,{resolve:t,reject:n})=>{Ab(),wb=t,xb=n,_b?(c(_b,e),_b.visible=!0):(_b=Wt(e),In((()=>(ty(bb,_b,Sb).mount(ny("u-s-a-s")),In((()=>_b.visible=!0))))))}),0,hp),Cb=Od("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:i})=>{(function(e,t,n){const o=document.fonts;if(o){const i=new FontFace(e,t,n);return i.load().then((()=>{o.add&&o.add(i)}))}return new Promise((o=>{const i=document.createElement("style"),r=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:i,variant:a,featureSettings:s}=n;e&&r.push(`font-style:${e}`),t&&r.push(`font-weight:${t}`),o&&r.push(`font-stretch:${o}`),i&&r.push(`unicode-range:${i}`),a&&r.push(`font-variant:${a}`),s&&r.push(`font-feature-settings:${s}`)}i.innerText=`@font-face{font-family:"${e}";src:${t};${r.join(";")}}`,document.head.appendChild(i),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Lu(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Lu(t.substring(4,t.length-1))}')`:Lu(t),n).then((()=>{o()})).catch((e=>{i(`loadFontFace:fail ${e}`)}))}));function Eb(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,ow.emit("onNavigationBarChange",{titleText:t})}to(t),Eo(t)}const kb=Od("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{!function(e,t,n,o,i){if(!e)return i("page not found");const{navigationBar:r}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:i,timingFunc:a}=o;e&&(r.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(r.backgroundColor=t),r.duration=i+"ms",r.timingFunc=a;break;case"showNavigationBarLoading":r.loading=!0;break;case"hideNavigationBarLoading":r.loading=!1;break;case"setNavigationBarTitle":const{title:s}=n;r.titleText=s}o()}(Lc(),"setNavigationBarTitle",e,t,n)})),Pb=Od("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(y(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:i,scrollHeight:r}=o;if(e=Math.min(e,r-i),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const a=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))};a(t)}(t||e||0,n),o()}),0,pp),Bb=Od("stopPullDownRefresh",((e,{resolve:t})=>{ow.invokeViewMethod("stopPullDownRefresh",{},Dc()),t()})),Mb=["text","iconPath","iconfont","selectedIconPath","visible"],Ib=["color","selectedColor","backgroundColor","borderStyle","midButton"],Ob=["badge","redDot"];function Lb(e,t,n){t.forEach((function(t){f(n,t)&&(e[t]=n[t])}))}function Db(e,t,n){const o=Yg();switch(e){case"showTabBar":o.shown=!0;break;case"hideTabBar":o.shown=!1;break;case"setTabBarItem":const{index:e}=t,n=o.list[e],i=n.pagePath;Lb(n,Mb,t);const{pagePath:r}=t;if(r){const t=de(r);t!==i&&function(e,t,n){const o=Uc(de(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const i=Uc(de(n));if(i){const{meta:t}=i;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=fe(n))}}(e,i,t)}break;case"setTabBarStyle":Lb(o,Ib,t);break;case"showTabBarRedDot":Lb(o.list[t.index],Ob,{badge:"",redDot:!0});break;case"setTabBarBadge":Lb(o.list[t.index],Ob,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":Lb(o.list[t.index],Ob,{badge:"",redDot:!1})}n()}const zb=Od("setTabBarItem",((e,{resolve:t})=>{Db("setTabBarItem",e,t)}),0,wp),Rb=Od("hideTabBar",((e,{resolve:t})=>{Db("hideTabBar",e||{},t)})),Nb=yu({name:"TabBar",setup(){const e=sn([]),t=Yg(),n=Jy(t,(()=>{const e=Yy(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}sn(c({type:"midButton"},e.midButton)),to(n)}(n,e),function(e){oo((()=>e.shown),(t=>{xc({"--window-bottom":nm(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return to((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,i=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=i}})),(t,n)=>()=>{const{pagePath:o,text:i}=t;let r=de(o);r===__uniRoutes[0].alias&&(r="/"),e.path!==r?Wy({from:"tabBar",url:r,tabBarText:i}):jc("onTabItemTap",{index:n,text:i,pagePath:o})}}(Sl(),n,e),{style:i,borderStyle:r,placeholderStyle:a}=function(e){const t=Er((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||em&&n&&"none"!==n&&(t=jb[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=Er((()=>{const{borderStyle:t}=e;return{backgroundColor:Fb[t]||t}})),o=Er((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return jo((()=>{n.iconfontSrc&&Cb({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:i,color:r}=e;return n.value.map(((n,a)=>{const s=o===a;return function(e,t,n,o,i,r,a,s){return rr("div",{key:a,class:"uni-tabbar__item",onClick:s(i,a)},[qb(e,t||"",n,o,i,r)],8,["onClick"])}(s?i:r,s&&n.selectedIconPath||n.iconPath||"",n.iconfont?s&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?s&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,a,t)}))}(n,o,e);return rr("uni-tabbar",{class:"uni-tabbar-"+n.position},[rr("div",{class:"uni-tabbar",style:i.value},[rr("div",{class:"uni-tabbar-border",style:r.value},null,4),t],4),rr("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});const jb={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},Fb={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function qb(e,t,n,o,i,r){const{height:a}=r;return rr("div",{class:"uni-tabbar__bd",style:{height:a}},[n?Hb(n,o||"rgb(0, 0, 0, 0.8)",i,r):t&&Vb(t,i,r),i.text&&Qb(e,i,r),i.redDot&&Ub(i.badge)],4)}function Vb(e,t,n){const{type:o,text:i}=t,{iconWidth:r}=n;return rr("div",{class:"uni-tabbar__icon"+(i?" uni-tabbar__icon__diff":""),style:{width:r,height:r}},["midButton"!==o&&rr("img",{src:Lu(e)},null,8,["src"])],6)}function Hb(e,t,n,o){var i;const{type:r,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:(null==(i=n.iconfont)?void 0:i.fontSize)||s,color:t};return rr("div",{class:l,style:c},["midButton"!==r&&rr("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Qb(e,t,n){const{iconPath:o,text:i}=t,{fontSize:r,spacing:a}=n;return rr("div",{class:"uni-tabbar__label",style:{color:e,fontSize:r,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"}},[i],4)}function Ub(e){return rr("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Wb=yu({name:"Layout",setup(e,{emit:t}){const n=sn(null);wc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=Sl();return{routeKey:Er((()=>um("/"+e.meta.route,$g()))),isTabBar:Er((()=>e.meta.isTabBar)),routeCache:fm}}(),{layoutState:i,windowState:r}=function(){Wg();{const e=Wt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return oo((()=>e.marginWidth),(e=>wc({"--window-margin":e+"px"}))),oo((()=>e.leftWindowWidth+e.marginWidth),(e=>{wc({"--window-left":e+"px"})})),oo((()=>e.rightWindowWidth+e.marginWidth),(e=>{wc({"--window-right":e+"px"})})),{layoutState:e,windowState:Er((()=>({})))}}}();!function(e,t){const n=Wg();function o(){const o=document.body.clientWidth,i=rm();let r={};if(i.length>0){r=i[i.length-1].$page.meta}else{const e=Uc(n.path,!0);e&&(r=e.meta)}const a=parseInt(String((f(r,"maxWidth")?r.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let s=!1;s=o>a,s&&a?(e.marginWidth=(o-a)/2,In((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+a+"px;margin:0 auto;")}))):(e.marginWidth=0,In((()=>{const e=t.value;e&&e.removeAttribute("style")})))}oo([()=>n.path],o),jo((()=>{o(),window.addEventListener("resize",o)}))}(i,n);const a=function(e){const t=Wg(),n=Yg(),o=Er((()=>t.meta.isTabBar&&n.shown));return wc({"--tab-bar-height":n.height}),o}(),s=function(e){const t=sn(!1);return Er((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(a);return()=>{const e=function(e,t,n,o,i,r){return function({routeKey:e,isTabBar:t,routeCache:n}){return rr(xl,null,{default:$n((({Component:o})=>[($i(),Ki(To,{matchBy:"key",cache:n},[($i(),Ki(Ko(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return Xo(rr(Nb,null,null,512),[[Ma,e.value]])}(a);return rr("uni-app",{ref:n,class:s.value},[e,t],2)}}});const $b=Id(0,(()=>{})),Xb=yu({name:"MapLocation",setup(){const e=Wt({latitude:0,longitude:0,rotate:0});{let t=function(t){e.rotate=t.direction},n=function(){zy({type:"gcj02",success:t=>{e.latitude=t.latitude,e.longitude=t.longitude},complete:()=>{r=setTimeout(n,3e4)}})},o=function(){r&&clearTimeout(r),Mv(t)};const i=eo("onMapReady");let r;Bv(t),i(n),Ho(o);const a=eo("addMapChidlContext"),s=eo("removeMapChidlContext"),l={id:"MAP_LOCATION",state:e};a(l),Ho((()=>s(l)))}return()=>e.latitude?rr(ov,fr({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:"data:image/png;base64,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"},e),null,16,["iconPath"]):null}}),Yb=yu({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){let t;return eo("onMapReady")(((n,o,i)=>{function r(){const{points:i,strokeWidth:r,strokeColor:a,dashArray:s,fillColor:l,zIndex:c}=e,u=i.map((e=>{const{latitude:t,longitude:n}=e;return tv()?[n,t]:nv()?new o.Point(n,t):new o.LatLng(t,n)})),{r:d,g:f,b:p,a:h}=iv(l),{r:g,g:m,b:v,a:y}=iv(a),b={clickable:!0,cursor:"crosshair",editable:!1,map:n,fillColor:"",path:u,strokeColor:"",strokeDashStyle:s.some((e=>e>0))?"dash":"solid",strokeWeight:r,visible:!0,zIndex:c};o.Color?(b.fillColor=new o.Color(d,f,p,h),b.strokeColor=new o.Color(g,m,v,y)):(b.fillColor=`rgb(${d}, ${f}, ${p})`,b.fillOpacity=h,b.strokeColor=`rgb(${g}, ${m}, ${v})`,b.strokeOpacity=y),t?t.setOptions(b):nv()?(t=new o.Polygon(b.path,b),n.addOverlay(t)):t=new o.Polygon(b)}r(),oo(e,r)})),Ho((()=>{t.setMap(null)})),()=>null}});function Jb(e){const t=[];return p(e)&&e.forEach((e=>{e&&e.latitude&&e.longitude&&t.push({latitude:e.latitude,longitude:e.longitude})})),t}function Gb(e,t,n){return nv()?function(e,t,n){return new e.Point(n,t)}(e,t,n):tv()?function(e,t,n){return new e.LngLat(n,t)}(e,t,n):function(e,t,n){return new e.LatLng(t,n)}(e,t,n)}function Kb(e){return"getLat"in e?e.getLat():nv()?e.lat:e.lat()}function Zb(e){return"getLng"in e?e.getLng():nv()?e.lng:e.lng()}function ew(e,t,n){const o=wu(t,n),i=sn(null);let r,a;const s=Wt({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:Jb(e.includePoints)}),l=[];let u,d;function f(e){u?e(a,r,o):l.push(e)}const p=[];function h(e){d?e():l.push(e)}const g={};function m(){const e=a.getCenter();return{scale:a.getZoom(),centerLocation:{latitude:Kb(e),longitude:Zb(e)}}}function v(){if(tv()){const e=[];s.includePoints.forEach((t=>{e.push([t.longitude,t.latitude])}));const t=new r.Bounds(...e);a.setBounds(t)}else if(nv());else{const e=new r.LatLngBounds;s.includePoints.forEach((({latitude:t,longitude:n})=>{const o=new r.LatLng(t,n);e.extend(o)})),a.fitBounds(e)}}function y(){const t=i.value,l=Gb(r,s.latitude,s.longitude),u=r.event||r.Event,f=new r.Map(t,{center:l,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});if(nv()&&(f.centerAndZoom(l,Number(e.scale)),f.enableScrollWheelZoom(),f._printLog&&f._printLog("uniapp")),oo((()=>e.scale),(e=>{f.setZoom(Number(e)||16)})),h((()=>{s.includePoints.length&&(v(),function(){const e=Gb(r,s.latitude,s.longitude);a.setCenter(e)}())})),nv())f.addEventListener("click",(()=>{o("tap",{},{}),o("click",{},{})})),f.addEventListener("dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),f.addEventListener("dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));else{const e=u.addListener(f,"bounds_changed",(()=>{e.remove(),d=!0,p.forEach((e=>e())),p.length=0}));u.addListener(f,"click",(()=>{o("tap",{},{}),o("click",{},{})})),u.addListener(f,"dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),u.addListener(f,"dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));const t=()=>{n("update:scale",f.getZoom()),o("regionchange",{},c({type:"end",causedBy:"scale"},m()))};u.addListener(f,"zoom_changed",t),u.addListener(f,"zoomend",t),u.addListener(f,"center_changed",(()=>{const e=f.getCenter(),t=Kb(e),o=Zb(e);n("update:latitude",t),n("update:longitude",o)}))}return f}oo([()=>e.latitude,()=>e.longitude],(([e,t])=>{const n=Number(e),o=Number(t);if((n!==s.latitude||o!==s.longitude)&&(s.latitude=n,s.longitude=o,a)){const e=Gb(r,s.latitude,s.longitude);a.setCenter(e)}})),oo((()=>e.includePoints),(e=>{s.includePoints=Jb(e),d&&v()}),{deep:!0});try{Ig(((e,t={})=>{switch(e){case"getCenterLocation":f((()=>{const n=a.getCenter();ge(t,{latitude:Kb(n),longitude:Zb(n),errMsg:`${e}:ok`})}));break;case"moveToLocation":{let n=Number(t.latitude),o=Number(t.longitude);if(!n||!o){const e=g.MAP_LOCATION;e&&(n=e.state.latitude,o=e.state.longitude)}if(n&&o){if(s.latitude=n,s.longitude=o,a){const e=Gb(r,n,o);a.setCenter(e)}f((()=>{ge(t,`${e}:ok`)}))}else ge(t,`${e}:fail`)}break;case"translateMarker":f((()=>{const n=g[t.markerId];if(n){try{n.translate(t)}catch(o){ge(t,`${e}:fail ${o.message}`)}ge(t,`${e}:ok`)}else ge(t,`${e}:fail not found`)}));break;case"includePoints":s.includePoints=Jb(t.includePoints),(d||tv())&&v(),h((()=>{ge(t,`${e}:ok`)}));break;case"getRegion":h((()=>{const n=a.getBounds(),o=n.getSouthWest(),i=n.getNorthEast();ge(t,{southwest:{latitude:Kb(o),longitude:Zb(o)},northeast:{latitude:Kb(i),longitude:Zb(i)},errMsg:`${e}:ok`})}));break;case"getScale":f((()=>{ge(t,{scale:a.getZoom(),errMsg:`${e}:ok`})}))}}),Lg(),!0)}catch(b){}return jo((()=>{Ym(e.libraries,(e=>{r=e,a=y(),u=!0,l.forEach((e=>e(a,r,o))),l.length=0,o("updated",{},{})}))})),Zn("onMapReady",f),Zn("addMapChidlContext",(function(e){g[e.id]=e})),Zn("removeMapChidlContext",(function(e){delete g[e.id]})),{state:s,mapRef:i,trigger:o}}const tw=vu({name:"Map",props:{id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},includePoints:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]}},emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,{emit:t,slots:n}){const o=sn(null),{mapRef:i,trigger:r}=ew(e,o,t);return()=>rr("uni-map",{ref:o,id:e.id},[rr("div",{ref:i,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map((e=>rr(ov,fr({key:e.id},e),null,16))),e.polyline.map((e=>rr(av,e,null,16))),e.circles.map((e=>rr(sv,e,null,16))),e.controls.map((e=>rr(cv,fr(e,{trigger:r}),null,16,["trigger"]))),e.showLocation&&rr(Xb,null,null),e.polygons.map((e=>rr(Yb,e,null,16))),rr("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[n.default&&n.default()])],8,["id"])}}),nw=c($l,{publishHandler(e,t,n){ow.subscribeHandler(e,t,n)}}),ow=c(iu,{publishHandler(e,t,n){nw.subscribeHandler(e,t,n)}}),iw=yu({name:"PageHead",setup(){const e=sn(null),t=Qg(),n=Jy(t.navigationBar,(()=>{const e=Yy(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:i}=function(e){const t=Er((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,i={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(i[`uni-page-head-shadow-${o}`]=!0),i})),n=Er((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const r=function(e,t){if(!t)return rr("div",{class:"uni-page-head-btn",onClick:aw},[Bc(Ec,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),a=n.type||"default",s="transparent"!==a&&"float"!==a&&rr("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return rr("uni-page-head",{"uni-page-head-type":a},[rr("div",{ref:e,class:o.value,style:i.value},[rr("div",{class:"uni-page-head-hd"},[r]),rw(n),rr("div",{class:"uni-page-head-ft"},[])],6),s],8,["uni-page-head-type"])}}});function rw(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:i}){return rr("div",{class:"uni-page-head-bd"},[rr("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?rr("i",{class:"uni-loading"},null):i?rr("img",{src:i,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function aw(){1===rm().length?Qy({url:"/"}):Fy({from:"backbutton",success(){}})}const sw={name:"PageRefresh",setup(){const{pullToRefresh:e}=Qg();return{offset:e.offset,color:e.color}}},lw=(e,t)=>{const n=e.__vccOpts||e;for(const[o,i]of t)n[o]=i;return n},cw={class:"uni-page-refresh-inner"},uw=["fill"],dw=[ir("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),ir("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],fw={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},pw=["stroke"];const hw=lw(sw,[["render",function(e,t,n,o,i,r){return $i(),Gi("uni-page-refresh",null,[ir("div",{style:le({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[ir("div",cw,[($i(),Gi("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},dw,8,uw)),($i(),Gi("svg",fw,[ir("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,pw)]))])],4)])}]]);function gw(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const mw="aborting",vw="refreshing",yw="restoring";function bw(e){const{id:t,pullToRefresh:n}=Qg(),{range:o,height:i}=n;let r,a,s,l,c,u,d,f;Ig((()=>{f||(f=vw,m(),setTimeout((()=>{x()}),50))}),"startPullDownRefresh",!1,t),Ig((()=>{f===vw&&(v(),f=yw,m(),function(e){if(!a)return;s.transition="-webkit-transform 0.3s",s.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",s.transform="translate3d(-50%, 0, 0)",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{v(),f=p=h=null})))}),"stopPullDownRefresh",!1,t),jo((()=>{r=e.value.$el,a=r.querySelector(".uni-page-refresh"),s=a.style,l=a.querySelector(".uni-page-refresh-inner").style}));let p=null,h=null;function g(e){f&&r&&r.classList[e]("uni-page-refresh--"+f)}function m(){g("add")}function v(){g("remove")}const y=bu((e=>{const t=e.changedTouches[0];c=t.identifier,u=t.pageY,d=!([mw,vw,yw].indexOf(f)>=0)})),b=bu((e=>{if(!d)return;if(!gw(e,c,u))return;let{deltaY:t}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(c=null);if(t<0&&!f)return;e.preventDefault(),null===p&&(h=t,f="pulling",m()),t-=h,t<0&&(t=0),p=t;(t>=o&&"reached"!==f||t<o&&"pulling"!==f)&&(v(),f="reached"===f?"pulling":"reached",m()),function(e){if(!a)return;let t=e/o;t>1?t=1:t*=t*t;const n=Math.round(e/(o/i))||0;l.transform="rotate("+360*t+"deg)",s.clip="rect("+(45-n)+"px,45px,45px,-5px)",s.transform="translate3d(-50%, "+n+"px, 0)"}(t)})),w=bu((e=>{gw(e,c,u)&&null!==f&&("pulling"===f?(v(),f=mw,m(),function(e){if(!a)return;if(s.transform){s.transition="-webkit-transform 0.3s",s.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{v(),f=p=h=null}))):"reached"===f&&(v(),f=vw,m(),x()))}));function x(){a&&(s.transition="-webkit-transform 0.2s",s.transform="translate3d(-50%, "+i+"px, 0)",jc(t,"onPullDownRefresh"))}return{onTouchstartPassive:y,onTouchmove:b,onTouchend:w,onTouchcancel:w}}const ww=yu({name:"PageBody",setup(e,t){const n=Qg(),o=sn(null),i=n.enablePullDownRefresh?bw(o):null;return()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return rr(hw,{ref:e},null,512)}(o,n);return rr(qi,null,[e,rr("uni-page-wrapper",i,[rr("uni-page-body",null,[oi(t.slots,"default")])],16)])}}});const xw=yu({name:"Page",setup(e,t){const n=Ug($g()),o=n.navigationBar;return Eb(n),()=>rr("uni-page",{"data-page":n.route},"custom"!==o.style?[rr(iw),_w(t)]:[_w(t)])}});function _w(e){return $i(),Ki(ww,{key:0},{default:$n((()=>[oi(e.slots,"page")])),_:3})}const Aw={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=Vd;const Sw=Object.assign({}),Tw=Object.assign;window.__uniConfig=Tw({globalStyle:{backgroundColor:"#fff",scrollIndicator:"none",bounce:"none",navigationBar:{backgroundColor:"#3A78FE",titleText:"uni-app",type:"default",titleColor:"#ffffff"},isNVue:!1},tabBar:{position:"bottom",color:"#000",selectedColor:"#4A88FB",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",list:[{pagePath:"pages/home/<USER>"},{pagePath:"pages/work/work"},{pagePath:"pages/contacts/contacts"},{pagePath:"pages/my/my"}],selectedIndex:0,shown:!0},compilerVersion:"4.06"},{appId:"__UNI__199BC86",appName:"移动门户",appVersion:"0.0.1",appVersionCode:1,async:Aw,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{maps:{amap:{key:"2588f9a78ee57e0d9bd2395665984f12",securityJsCode:"d4c7f0e24a49f8e0b84f68a2e652816b",serviceHost:""}}},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:"2588f9a78ee57e0d9bd2395665984f12",aMapSecurityJsCode:"d4c7f0e24a49f8e0b84f68a2e652816b",aMapServiceHost:"",nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Sw).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Tw(e[n]||(e[n]={}),Sw[t].default),e}),{}),router:{mode:"history",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Cw={delay:Aw.delay,timeout:Aw.timeout,suspensible:Aw.suspensible};Aw.loading&&(Cw.loadingComponent={name:"SystemAsyncLoading",render:()=>rr(Jo(Aw.loading))}),Aw.error&&(Cw.errorComponent={name:"SystemAsyncError",render:()=>rr(Jo(Aw.error))});const Ew=()=>t((()=>import("./pages-home-home.8d9e398b.js")),["assets/pages-home-home.8d9e398b.js","assets/yuni-title.244fda8d.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/staticUrl.6af4fd3e.js","assets/yuni-title-16d11cf3.css","assets/u--image.daf5e625.js","assets/u-image.5419d1e1.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-tabbar.1db2d7be.js","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/yuni-tabbar-e23b24dc.css","assets/uniUtils.7f6d2e72.js","assets/notifyApi.97cd68fe.js","assets/request.a87c83d2.js","assets/home-bab69f68.css"]).then((e=>Om(e.default||e))),kw=wo(Tw({loader:Ew},Cw)),Pw=()=>t((()=>import("./pages-index-index.911c18f0.js")),["assets/pages-index-index.911c18f0.js","assets/u--input.8a6cd2c5.js","assets/u-input.05fd69cd.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-input-f05bf92a.css","assets/u--form.28cd79d6.js","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/u--form-2a76d277.css","assets/u-button.263dfc4b.js","assets/u-loading-icon.2e7e7b55.js","assets/u-loading-icon-2847e2e1.css","assets/u-button-bb4e7504.css","assets/u-popup.1301b1bc.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-status-bar.0be47f49.js","assets/u-status-bar-aadaac91.css","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/u-popup-12e042e6.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/staticUrl.6af4fd3e.js","assets/crypto.39557b8f.js","assets/index.a555f9dd.js","assets/_commonjsHelpers.02d3be64.js","assets/index-b378b926.css"]).then((e=>Om(e.default||e))),Bw=wo(Tw({loader:Pw},Cw)),Mw=()=>t((()=>import("./pages-login-login.64244e5b.js")),["assets/pages-login-login.64244e5b.js","assets/u--image.daf5e625.js","assets/u-image.5419d1e1.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/u-input.05fd69cd.js","assets/u-input-f05bf92a.css","assets/u--form.28cd79d6.js","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/u--form-2a76d277.css","assets/u-button.263dfc4b.js","assets/u-loading-icon.2e7e7b55.js","assets/u-loading-icon-2847e2e1.css","assets/u-button-bb4e7504.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/sm2Encryptor.e35ab258.js","assets/index.a555f9dd.js","assets/_commonjsHelpers.02d3be64.js","assets/staticUrl.6af4fd3e.js","assets/login-eb5db80b.css"]).then((e=>Om(e.default||e))),Iw=wo(Tw({loader:Mw},Cw)),Ow=()=>t((()=>import("./pages-work-work.fa12085b.js")),["assets/pages-work-work.fa12085b.js","assets/yuni-nav-bar.1e642613.js","assets/u-status-bar.0be47f49.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-status-bar-aadaac91.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-image.5419d1e1.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-nav-bar-be18b33f.css","assets/u--image.daf5e625.js","assets/uni-icons.3c47aaf1.js","assets/uni-icons-0f73295e.css","assets/yuni-tabbar.1db2d7be.js","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/yuni-tabbar-e23b24dc.css","assets/staticUrl.6af4fd3e.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/crypto.39557b8f.js","assets/index.a555f9dd.js","assets/_commonjsHelpers.02d3be64.js","assets/work-b26934b7.css"]).then((e=>Om(e.default||e))),Lw=wo(Tw({loader:Ow},Cw)),Dw=()=>t((()=>import("./pages-contacts-contacts.a500d416.js")),["assets/pages-contacts-contacts.a500d416.js","assets/yuni-nav-bar.1e642613.js","assets/u-status-bar.0be47f49.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-status-bar-aadaac91.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-image.5419d1e1.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-nav-bar-be18b33f.css","assets/u-search.e7354f60.js","assets/u-search-159c04ae.css","assets/uni-list.e49ca7b6.js","assets/uni-icons.3c47aaf1.js","assets/uni-icons-0f73295e.css","assets/uni-list-b66a1176.css","assets/yuni-tabbar.1db2d7be.js","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/yuni-tabbar-e23b24dc.css","assets/staticUrl.6af4fd3e.js","assets/contacts-402d326f.css"]).then((e=>Om(e.default||e))),zw=wo(Tw({loader:Dw},Cw)),Rw=()=>t((()=>import("./pages-my-my.fb947ed0.js")),["assets/pages-my-my.fb947ed0.js","assets/u--image.daf5e625.js","assets/u-image.5419d1e1.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/uni-icons.3c47aaf1.js","assets/uni-icons-0f73295e.css","assets/yuni-tabbar.1db2d7be.js","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/yuni-tabbar-e23b24dc.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/staticUrl.6af4fd3e.js","assets/my-aa6dce85.css"]).then((e=>Om(e.default||e))),Nw=wo(Tw({loader:Rw},Cw)),jw=()=>t((()=>import("./pages-generalPage-forgetPwd-forgetPwd.c4bf5a85.js")),["assets/pages-generalPage-forgetPwd-forgetPwd.c4bf5a85.js","assets/yuni-title.244fda8d.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/staticUrl.6af4fd3e.js","assets/yuni-title-16d11cf3.css","assets/u--input.8a6cd2c5.js","assets/u-input.05fd69cd.js","assets/u-input-f05bf92a.css","assets/u--form.28cd79d6.js","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/u--form-2a76d277.css","assets/u-button.263dfc4b.js","assets/u-loading-icon.2e7e7b55.js","assets/u-loading-icon-2847e2e1.css","assets/u-button-bb4e7504.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/sm2Encryptor.e35ab258.js","assets/index.a555f9dd.js","assets/_commonjsHelpers.02d3be64.js","assets/forgetPwd-b9b5e30d.css"]).then((e=>Om(e.default||e))),Fw=wo(Tw({loader:jw},Cw)),qw=()=>t((()=>import("./pages-generalPage-notifyDetail-notifyDetail.8446b050.js")),["assets/pages-generalPage-notifyDetail-notifyDetail.8446b050.js","assets/notifyApi.97cd68fe.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/notifyDetail-23266a74.css"]).then((e=>Om(e.default||e))),Vw=wo(Tw({loader:qw},Cw)),Hw=()=>t((()=>import("./pages-generalPage-notify-notify.ff487148.js")),["assets/pages-generalPage-notify-notify.ff487148.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/notifyApi.97cd68fe.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/notify-89e0b792.css"]).then((e=>Om(e.default||e))),Qw=wo(Tw({loader:Hw},Cw)),Uw=()=>t((()=>import("./pages-generalPage-notice-notice.8871148b.js")),["assets/pages-generalPage-notice-notice.8871148b.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/notifyApi.97cd68fe.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/notice-5ac4eb3f.css"]).then((e=>Om(e.default||e))),Ww=wo(Tw({loader:Uw},Cw)),$w=()=>t((()=>import("./pages-generalPage-contactsList-contactsList.53756b6d.js")),["assets/pages-generalPage-contactsList-contactsList.53756b6d.js","assets/yuni-nav-bar.1e642613.js","assets/u-status-bar.0be47f49.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-status-bar-aadaac91.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-image.5419d1e1.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-nav-bar-be18b33f.css","assets/u-search.e7354f60.js","assets/u-search-159c04ae.css","assets/uni-list.e49ca7b6.js","assets/uni-icons.3c47aaf1.js","assets/uni-icons-0f73295e.css","assets/uni-list-b66a1176.css","assets/address.c296549f.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/staticUrl.6af4fd3e.js","assets/contactsList-0d7d24d7.css"]).then((e=>Om(e.default||e))),Xw=wo(Tw({loader:$w},Cw)),Yw=()=>t((()=>import("./pages-generalPage-contactsDetail-contactsDetail.9900a678.js")),["assets/pages-generalPage-contactsDetail-contactsDetail.9900a678.js","assets/u-search.e7354f60.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-search-159c04ae.css","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/staticUrl.6af4fd3e.js","assets/address.c296549f.js","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/contactsDetail-9294878e.css"]).then((e=>Om(e.default||e))),Jw=wo(Tw({loader:Yw},Cw)),Gw=()=>t((()=>import("./pages-generalPage-addressDetail-addressDetail.fcb762b6.js")),["assets/pages-generalPage-addressDetail-addressDetail.fcb762b6.js","assets/yuni-nav-bar.1e642613.js","assets/u-status-bar.0be47f49.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-status-bar-aadaac91.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-image.5419d1e1.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-nav-bar-be18b33f.css","assets/staticUrl.6af4fd3e.js","assets/addressDetail-df3632b3.css"]).then((e=>Om(e.default||e))),Kw=wo(Tw({loader:Gw},Cw)),Zw=()=>t((()=>import("./pages-generalPage-about-about.fa1fdeba.js")),["assets/pages-generalPage-about-about.fa1fdeba.js","assets/u--image.daf5e625.js","assets/u-image.5419d1e1.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-image-e1ccce93.css","assets/yuni-title.244fda8d.js","assets/staticUrl.6af4fd3e.js","assets/yuni-title-16d11cf3.css","assets/about-e894c5b9.css"]).then((e=>Om(e.default||e))),ex=wo(Tw({loader:Zw},Cw)),tx=()=>t((()=>import("./pages-generalPage-noauthority-noauthority.248c21ff.js")),["assets/pages-generalPage-noauthority-noauthority.248c21ff.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/noauthority-a329017e.css"]).then((e=>Om(e.default||e))),nx=wo(Tw({loader:tx},Cw)),ox=()=>t((()=>import("./pages-generalPage-agreement-agreement.b1f55a35.js")),["assets/pages-generalPage-agreement-agreement.b1f55a35.js","assets/u-parse.f281f4ef.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse-5d370bbd.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/agreement-9d509e9b.css"]).then((e=>Om(e.default||e))),ix=wo(Tw({loader:ox},Cw)),rx=()=>t((()=>import("./pages-generalPage-privacy-privacy.d823c2df.js")),["assets/pages-generalPage-privacy-privacy.d823c2df.js","assets/u-parse.f281f4ef.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse-5d370bbd.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/privacy-1b191539.css"]).then((e=>Om(e.default||e))),ax=wo(Tw({loader:rx},Cw)),sx=()=>t((()=>import("./pages-UNI-webview-webview.c90f5ec7.js")),["assets/pages-UNI-webview-webview.c90f5ec7.js","assets/uniUtils.7f6d2e72.js"]).then((e=>Om(e.default||e))),lx=wo(Tw({loader:sx},Cw)),cx=()=>t((()=>import("./pages-UNI-network-networkError.1013f430.js")),["assets/pages-UNI-network-networkError.1013f430.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/networkError-37f9e5df.css"]).then((e=>Om(e.default||e))),ux=wo(Tw({loader:cx},Cw)),dx=()=>t((()=>import("./pages-UNI-netError-netError.3f482b61.js")),["assets/pages-UNI-netError-netError.3f482b61.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/staticUrl.6af4fd3e.js","assets/netError-ba9f3cb1.css"]).then((e=>Om(e.default||e))),fx=wo(Tw({loader:dx},Cw)),px=()=>t((()=>import("./pages-comExample-textToVoice-textToVoice.b1e1dab3.js")),["assets/pages-comExample-textToVoice-textToVoice.b1e1dab3.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/textToVoice-b13c7d43.css"]).then((e=>Om(e.default||e))),hx=wo(Tw({loader:px},Cw)),gx=()=>t((()=>import("./pages-comExample-tree-tree.a704d1da.js")),["assets/pages-comExample-tree-tree.a704d1da.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u--form.28cd79d6.js","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/u--form-2a76d277.css","assets/tree-cf732ea2.css"]).then((e=>Om(e.default||e))),mx=wo(Tw({loader:gx},Cw)),vx=()=>t((()=>import("./pages-comExample-comIndex-comIndex.1cce014f.js")),["assets/pages-comExample-comIndex-comIndex.1cce014f.js","assets/uni-icons.3c47aaf1.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-icons-0f73295e.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/comIndex-945eafbc.css"]).then((e=>Om(e.default||e))),yx=wo(Tw({loader:vx},Cw)),bx=()=>t((()=>import("./pages-comExample-socket-socket.441fce54.js")),["assets/pages-comExample-socket-socket.441fce54.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/socket-fec29056.css"]).then((e=>Om(e.default||e))),wx=wo(Tw({loader:bx},Cw)),xx=()=>t((()=>import("./pages-comExample-socket-websocket.19cb5d7a.js")),["assets/pages-comExample-socket-websocket.19cb5d7a.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/websocket-3f7c5c1b.css"]).then((e=>Om(e.default||e))),_x=wo(Tw({loader:xx},Cw)),Ax=()=>t((()=>import("./pages-comExample-socket-socketTask.865c8992.js")),["assets/pages-comExample-socket-socketTask.865c8992.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/socketTask-dd3f2669.css"]).then((e=>Om(e.default||e))),Sx=wo(Tw({loader:Ax},Cw)),Tx=()=>t((()=>import("./pages-comExample-file-download-file-download.2ce995ba.js")),["assets/pages-comExample-file-download-file-download.2ce995ba.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-loading-icon.2e7e7b55.js","assets/u-loading-icon-2847e2e1.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/file-download-118b6d82.css"]).then((e=>Om(e.default||e))),Cx=wo(Tw({loader:Tx},Cw)),Ex=()=>t((()=>import("./pages-comExample-signature-signature.d60a9cf4.js")),["assets/pages-comExample-signature-signature.d60a9cf4.js","assets/u-button.263dfc4b.js","assets/u-loading-icon.2e7e7b55.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-loading-icon-2847e2e1.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-button-bb4e7504.css","assets/signature-516b1d48.css"]).then((e=>Om(e.default||e))),kx=wo(Tw({loader:Ex},Cw)),Px=()=>t((()=>import("./pages-comExample-editRichText-editRichText.d89b3135.js")),["assets/pages-comExample-editRichText-editRichText.d89b3135.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/editRichText-baa8c909.css"]).then((e=>Om(e.default||e))),Bx=wo(Tw({loader:Px},Cw)),Mx=()=>t((()=>import("./pages-comExample-debounceAndThrottle-debounceAndThrottle.f4530dfc.js")),["assets/pages-comExample-debounceAndThrottle-debounceAndThrottle.f4530dfc.js","assets/u--input.8a6cd2c5.js","assets/u-input.05fd69cd.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-input-f05bf92a.css","assets/debounceAndThrottle-71cd0a9d.css"]).then((e=>Om(e.default||e))),Ix=wo(Tw({loader:Mx},Cw)),Ox=()=>t((()=>import("./pages-comExample-mapDemo-map.b5627564.js")),["assets/pages-comExample-mapDemo-map.b5627564.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/map-5b8f28f9.css"]).then((e=>Om(e.default||e))),Lx=wo(Tw({loader:Ox},Cw)),Dx=()=>t((()=>import("./pages-comExample-uchartsDemo-uchartsDemo.908c0a8c.js")),["assets/pages-comExample-uchartsDemo-uchartsDemo.908c0a8c.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uchartsDemo-0fa1341f.css"]).then((e=>Om(e.default||e))),zx=wo(Tw({loader:Dx},Cw)),Rx=()=>t((()=>import("./pages-comExample-bpmnDemo-bpmnDemo.7ffb535d.js")),["assets/pages-comExample-bpmnDemo-bpmnDemo.7ffb535d.js","assets/_commonjsHelpers.02d3be64.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/bpmnDemo-9225e5ab.css"]).then((e=>Om(e.default||e))),Nx=wo(Tw({loader:Rx},Cw)),jx=()=>t((()=>import("./pages-comExample-tencentLocation-tencentLocation.0e3fa66d.js")),["assets/pages-comExample-tencentLocation-tencentLocation.0e3fa66d.js","assets/staticUrl.6af4fd3e.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/tencentLocation-66c01945.css"]).then((e=>Om(e.default||e))),Fx=wo(Tw({loader:jx},Cw)),qx=()=>t((()=>import("./pages-comExample-xgplay-xgplay.a9cd7904.js")),["assets/pages-comExample-xgplay-xgplay.a9cd7904.js","assets/_plugin-vue_export-helper.1b428a4d.js"]).then((e=>Om(e.default||e))),Vx=wo(Tw({loader:qx},Cw)),Hx=()=>t((()=>import("./pages-comExample-xgplayerDemo-xgplayerDemo.c8285bab.js")),["assets/pages-comExample-xgplayerDemo-xgplayerDemo.c8285bab.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/_commonjsHelpers.02d3be64.js","assets/xgplayerDemo-3b2def7f.css"]).then((e=>Om(e.default||e))),Qx=wo(Tw({loader:Hx},Cw)),Ux=()=>t((()=>import("./pages-comExample-gisDemo-gisDemo.a785c2f4.js")),["assets/pages-comExample-gisDemo-gisDemo.a785c2f4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/gisDemo-ffff26fb.css"]).then((e=>Om(e.default||e))),Wx=wo(Tw({loader:Ux},Cw)),$x=()=>t((()=>import("./pages-comExample-drawLock-drawLock.8a234c4d.js")),["assets/pages-comExample-drawLock-drawLock.8a234c4d.js","assets/_plugin-vue_export-helper.1b428a4d.js"]).then((e=>Om(e.default||e))),Xx=wo(Tw({loader:$x},Cw)),Yx=()=>t((()=>import("./pages-comExample-fileSelect-fileSelect.6bb7d078.js")),["assets/pages-comExample-fileSelect-fileSelect.6bb7d078.js","assets/u-button.263dfc4b.js","assets/u-loading-icon.2e7e7b55.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-loading-icon-2847e2e1.css","assets/u-icon.66912310.js","assets/u-icon-d34a0ef2.css","assets/u-button-bb4e7504.css","assets/u-popup.1301b1bc.js","assets/u-transition.dd4c905d.js","assets/u-transition-379d6577.css","assets/u-status-bar.0be47f49.js","assets/u-status-bar-aadaac91.css","assets/u-safe-bottom.c99871d2.js","assets/u-safe-bottom-f437a679.css","assets/u-popup-12e042e6.css","assets/request.a87c83d2.js","assets/uniUtils.7f6d2e72.js","assets/u--form.28cd79d6.js","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/u--form-2a76d277.css","assets/fileSelect-e319b40e.css"]).then((e=>Om(e.default||e))),Jx=wo(Tw({loader:Yx},Cw)),Gx=()=>t((()=>import("./pages-comExample-trendsPermission-trendsPermission.0a00700b.js")),["assets/pages-comExample-trendsPermission-trendsPermission.0a00700b.js","assets/u-icon.66912310.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-d34a0ef2.css","assets/u-line.7fe8626e.js","assets/u-line-f1c498d5.css","assets/trendsPermission-0e492230.css"]).then((e=>Om(e.default||e))),Kx=wo(Tw({loader:Gx},Cw));function Zx(e,t){return $i(),Ki(xw,null,{page:$n((()=>[rr(e,Tw({},t,{ref:"page"}),null,512)])),_:1})}function e_(e,t){return"string"==typeof e?t:e}window.__uniRoutes=[{path:"/",alias:"/pages/home/<USER>",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(kw,t)}},loader:Ew,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,enablePullDownRefresh:!1,navigationBar:{titleText:"首页",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/index/index",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Bw,t)}},loader:Pw,meta:{navigationBar:{titleText:"uni-app",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Iw,t)}},loader:Mw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/work/work",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Lw,t)}},loader:Ow,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,enablePullDownRefresh:!1,navigationBar:{titleText:"工作台",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/contacts/contacts",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(zw,t)}},loader:Dw,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,enablePullDownRefresh:!1,navigationBar:{titleText:"通讯录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/my/my",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Nw,t)}},loader:Rw,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,enablePullDownRefresh:!1,bounce:"none",navigationBar:{titleText:"我的",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/generalPage/forgetPwd/forgetPwd",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Fw,t)}},loader:jw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"找回密码",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/generalPage/notifyDetail/notifyDetail",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Vw,t)}},loader:qw,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"通知详情",type:"default"},isNVue:!1}},{path:"/pages/generalPage/notify/notify",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Qw,t)}},loader:Hw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"公告列表页",type:"default"},isNVue:!0}},{path:"/pages/generalPage/notice/notice",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Ww,t)}},loader:Uw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"公告列表页",type:"default"},isNVue:!1}},{path:"/pages/generalPage/contactsList/contactsList",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Xw,t)}},loader:$w,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/generalPage/contactsDetail/contactsDetail",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Jw,t)}},loader:Yw,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/generalPage/addressDetail/addressDetail",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Kw,t)}},loader:Gw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/generalPage/about/about",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(ex,t)}},loader:Zw,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"关于我们",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/generalPage/noauthority/noauthority",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(nx,t)}},loader:tx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/generalPage/agreement/agreement",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(ix,t)}},loader:ox,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"用户协议",type:"default"},isNVue:!1}},{path:"/pages/generalPage/privacy/privacy",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(ax,t)}},loader:rx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"隐私政策",type:"default"},isNVue:!1}},{path:"/pages/UNI/webview/webview",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(lx,t)}},loader:sx,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"默认页面",type:"default"},isNVue:!1}},{path:"/pages/UNI/network/networkError",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(ux,t)}},loader:cx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"无网络连接",type:"default"},isNVue:!1}},{path:"/pages/UNI/netError/netError",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(fx,t)}},loader:dx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"网络检测示例",type:"default"},isNVue:!1}},{path:"/pages/comExample/textToVoice/textToVoice",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(hx,t)}},loader:px,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"文字转语音",type:"default"},isNVue:!1}},{path:"/pages/comExample/tree/tree",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(mx,t)}},loader:gx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"组织树",type:"default"},isNVue:!1}},{path:"/pages/comExample/comIndex/comIndex",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(yx,t)}},loader:vx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"组件示例",type:"default"},isNVue:!1}},{path:"/pages/comExample/socket/socket",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(wx,t)}},loader:bx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"socket连接",type:"default"},isNVue:!1}},{path:"/pages/comExample/socket/websocket",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(_x,t)}},loader:xx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"websocket",type:"default"},isNVue:!1}},{path:"/pages/comExample/socket/socketTask",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Sx,t)}},loader:Ax,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"socketTask",type:"default"},isNVue:!1}},{path:"/pages/comExample/file-download/file-download",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Cx,t)}},loader:Tx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"图片上传下载",type:"default"},isNVue:!1}},{path:"/pages/comExample/signature/signature",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(kx,t)}},loader:Ex,meta:{enablePullDownRefresh:!1,bounce:"none",navigationBar:{titleText:"手写签名",type:"default"},isNVue:!1}},{path:"/pages/comExample/editRichText/editRichText",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Bx,t)}},loader:Px,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"富文本编辑",type:"default"},isNVue:!1}},{path:"/pages/comExample/debounceAndThrottle/debounceAndThrottle",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Ix,t)}},loader:Mx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"防抖节流",type:"default"},isNVue:!1}},{path:"/pages/comExample/mapDemo/map",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Lx,t)}},loader:Ox,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"地图",type:"default"},isNVue:!1}},{path:"/pages/comExample/uchartsDemo/uchartsDemo",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(zx,t)}},loader:Dx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"ucharts示例",type:"default"},isNVue:!1}},{path:"/pages/comExample/bpmnDemo/bpmnDemo",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Nx,t)}},loader:Rx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"流程化工具bpmn",type:"default"},isNVue:!1}},{path:"/pages/comExample/tencentLocation/tencentLocation",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Fx,t)}},loader:jx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/comExample/xgplay/xgplay",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Vx,t)}},loader:qx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"西瓜播放器",type:"default"},isNVue:!1}},{path:"/pages/comExample/xgplayerDemo/xgplayerDemo",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Qx,t)}},loader:Hx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/comExample/gisDemo/gisDemo",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Wx,t)}},loader:Ux,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"gis示例",type:"default"},isNVue:!1}},{path:"/pages/comExample/drawLock/drawLock",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Xx,t)}},loader:$x,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"图形解锁",type:"default"},isNVue:!1}},{path:"/pages/comExample/fileSelect/fileSelect",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Jx,t)}},loader:Yx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"动态权限申请",type:"default"},isNVue:!1}},{path:"/pages/comExample/trendsPermission/trendsPermission",component:{setup(){const e=Pm(),t=e&&e.$route&&e.$route.query||{};return()=>Zx(Kx,t)}},loader:Gx,meta:{enablePullDownRefresh:!1,navigationBar:{titleText:"动态权限专题",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const t_=e=>(t,n=vr())=>{!xr&&zo(e,t,n)},n_=t_("onShow"),o_=t_("onLaunch"),i_=t_("onLoad"),r_=t_("onReady"),a_=t_("onUnload"),s_=t_("onPageScroll"),l_=t_("onPullDownRefresh");
/*!
  * pinia v2.0.33
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */
let c_;const u_=e=>c_=e,d_=Symbol();function f_(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var p_,h_;(h_=p_||(p_={})).direct="direct",h_.patchObject="patch object",h_.patchFunction="patch function";const g_="undefined"!=typeof window;function m_(){const e=je(!0),t=e.run((()=>sn({})));let n=[],o=[];const i=en({install(e){u_(i),i._a=e,e.provide(d_,i),e.config.globalProperties.$pinia=i,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const v_=()=>{};function y_(e,t,n,o=v_){e.push(t);const i=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};var r;return!n&&Fe()&&(r=i,Re&&Re.cleanups.push(r)),i}function b_(e,...t){e.slice().forEach((e=>{e(...t)}))}function w_(e,t){e instanceof Map&&t instanceof Map&&t.forEach(((t,n)=>e.set(n,t))),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],i=e[n];f_(i)&&f_(o)&&e.hasOwnProperty(n)&&!an(o)&&!Yt(o)?e[n]=w_(i,o):e[n]=o}return e}const x_=Symbol();const{assign:__}=Object;function A_(e,t,n,o){const{state:i,actions:r,getters:a}=t,s=n.state.value[e];let l;return l=S_(e,(function(){s||(n.state.value[e]=i?i():{});const t=function(e){const t=p(e)?new Array(e.length):{};for(const n in e)t[n]=vn(e,n);return t}(n.state.value[e]);return __(t,r,Object.keys(a||{}).reduce(((t,o)=>(t[o]=en(Er((()=>{u_(n);const t=n._s.get(e);return a[o].call(t,t)}))),t)),{}))}),t,n,o,!0),l}function S_(e,t,n={},o,i,r){let a;const s=__({actions:{}},n),l={deep:!0};let c,u,d,f=en([]),p=en([]);const h=o.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:p_.patchFunction,storeId:e,events:d}):(w_(o.state.value[e],t),n={type:p_.patchObject,payload:t,storeId:e,events:d});const i=g=Symbol();In().then((()=>{g===i&&(c=!0)})),u=!0,b_(f,n,o.state.value[e])}r||h||(o.state.value[e]={}),sn({});const v=r?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{__(e,t)}))}:v_;function y(t,n){return function(){u_(o);const i=Array.from(arguments),r=[],a=[];function s(e){r.push(e)}function l(e){a.push(e)}let c;b_(p,{args:i,name:t,store:b,after:s,onError:l});try{c=n.apply(this&&this.$id===e?this:b,i)}catch(u){throw b_(a,u),u}return c instanceof Promise?c.then((e=>(b_(r,e),e))).catch((e=>(b_(a,e),Promise.reject(e)))):(b_(r,c),c)}}const b=Wt({_p:o,$id:e,$onAction:y_.bind(null,p),$patch:m,$reset:v,$subscribe(t,n={}){const i=y_(f,t,n.detached,(()=>r())),r=a.run((()=>oo((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:p_.direct,events:d},o)}),__({},l,n))));return i},$dispose:function(){a.stop(),f=[],p=[],o._s.delete(e)}});o._s.set(e,b);const w=o._e.run((()=>(a=je(),a.run((()=>t())))));for(const A in w){const t=w[A];if(an(t)&&(!an(_=t)||!_.effect)||Yt(t))r||(!h||f_(x=t)&&x.hasOwnProperty(x_)||(an(t)?t.value=h[A]:w_(t,h[A])),o.state.value[e][A]=t);else if("function"==typeof t){const e=y(A,t);w[A]=e,s.actions[A]=t}}var x,_;return __(b,w),__(Zt(b),w),Object.defineProperty(b,"$state",{get:()=>o.state.value[e],set:e=>{m((t=>{__(t,e)}))}}),o._p.forEach((e=>{__(b,a.run((()=>e({store:b,app:o._a,pinia:o,options:s}))))})),h&&r&&n.hydrate&&n.hydrate(b.$state,h),c=!0,u=!0,b}function T_(e,t,n){let o,i;const r="function"==typeof t;function a(e,n){const a=vr();(e=e||a&&eo(d_,null))&&u_(e),(e=c_)._s.has(o)||(r?S_(o,t,i,e):A_(o,i,e));return e._s.get(o)}return"string"==typeof e?(o=e,i=r?n:t):(i=e,o=e.id),a.$id=o,a}let C_="Store";function E_(e,t){return Array.isArray(t)?t.reduce(((t,n)=>(t[n]=function(){return e(this.$pinia)[n]},t)),{}):Object.keys(t).reduce(((n,o)=>(n[o]=function(){const n=e(this.$pinia),i=t[o];return"function"==typeof i?i.call(this,n):n[i]},n)),{})}const k_=E_;const P_=Object.freeze(Object.defineProperty({__proto__:null,get MutationType(){return p_},PiniaVuePlugin:function(e){e.mixin({beforeCreate(){const e=this.$options;if(e.pinia){const t=e.pinia;if(!this._provided){const e={};Object.defineProperty(this,"_provided",{get:()=>e,set:t=>Object.assign(e,t)})}this._provided[d_]=t,this.$pinia||(this.$pinia=t),t._a=this,g_&&u_(t)}else!this.$pinia&&e.parent&&e.parent.$pinia&&(this.$pinia=e.parent.$pinia)},destroyed(){delete this._pStores}})},acceptHMRUpdate:function(e,t){return()=>{}},createPinia:m_,defineStore:T_,getActivePinia:()=>vr()&&eo(d_)||c_,mapActions:function(e,t){return Array.isArray(t)?t.reduce(((t,n)=>(t[n]=function(...t){return e(this.$pinia)[n](...t)},t)),{}):Object.keys(t).reduce(((n,o)=>(n[o]=function(...n){return e(this.$pinia)[t[o]](...n)},n)),{})},mapGetters:k_,mapState:E_,mapStores:function(...e){return e.reduce(((e,t)=>(e[t.$id+C_]=function(){return t(this.$pinia)},e)),{})},mapWritableState:function(e,t){return Array.isArray(t)?t.reduce(((t,n)=>(t[n]={get(){return e(this.$pinia)[n]},set(t){return e(this.$pinia)[n]=t}},t)),{}):Object.keys(t).reduce(((n,o)=>(n[o]={get(){return e(this.$pinia)[t[o]]},set(n){return e(this.$pinia)[t[o]]=n}},n)),{})},setActivePinia:u_,setMapStoreSuffix:function(e){C_=e},skipHydrate:function(e){return Object.defineProperty(e,x_,{})},storeToRefs:function(e){{e=Zt(e);const t={};for(const n in e){const o=e[n];(an(o)||Yt(o))&&(t[n]=vn(e,n))}return t}}},Symbol.toStringTag,{value:"Module"})),B_=T_("token",{state:()=>({refreshToken:{expiration:0,value:""},tokenType:"",value:"",expiration:0,client_secret:"58ea04ba02475c8da2321cc99849d2a10f15b749",clientId:"",userid:"",tenantId:"",tenantName:"",registerId:"",isNotify:!1,isloginOut:!1}),actions:{clearAuthInfo(){this.refreshToken={},this.tokenType="",this.value="",this.expiration=0,this.clientId="",this.userid=""}},getters:{getTitle:e=>e.client_secret},unistorage:!0}),M_=Nv("colorIndex")||0;var I_;I_={mock:false,storeSuffix:"door",version:"20211231-21:10",versionNum:2,maxVideoSize:314572800,maxVideoMsg:"视频文件大小超过300MB，请重新上传！",clientId:"bVS46ElU",appCode:"ydmh",tenantId_global:"system",BASEURL:"/prod-api",QQMapWXKey:"UN7BZ-UEOKU-O7BVN-GORR2-ARFZ3-STFX6",AMapKey:"6a3c7c68739e5969d17ec976a7e5dedd",colorIndex:M_,colorList:["#007AFF","#FF0000","#00FF00"],IMAGE_URL:"http://172.16.11.164:32489/portal/unidoor_hm",SOCKETURL:"",LOGIN_TITLE:"智慧园区平台",LOGIN_BGIMG:"http://172.16.11.168:30174/zfc/2023/09/19/d7f518f2-d1a9-441d-a8a2-09d91dcc8022.png",defalutOutLink:"https://www.baidu.com"};const O_=T_("user",{state:()=>({userInfo:{authorityList:[],authorities:[],userJobDetailVOList:[],scope:[],currentOrgName:"",city:"",staffKind:"",orgList:"",userid:"",currentOrgId:"",enabled:!0,orgId:"",client_id:"",userPwdFlag:0,province:"",cityName:"",orgCode:"",loginName:"",staffName:"",cellphone:"",tel:"",exp:0,email:"",townName:"",orgName:"",districtName:"",town:"",isquxian:"",active:!0,staffType:"",customParam:{},staffOrgId:"",district:"",tenantId:"",provinceName:"",staffId:"",username:""}}),actions:{},unistorage:!0});const L_={tab1:[{pagePath:"/pages/home/<USER>",text:"首页",iconPath:"/static/images/home.png",selectedIconPath:"/static/images/home_select.png"},{pagePath:"/pages/work/work",text:"工作台",iconPath:"/static/images/serve.png",selectedIconPath:"/static/images/serve_select.png"},{pagePath:"/pages/my/my",text:"我的",iconPath:"/static/images/my.png",selectedIconPath:"/static/images/my_select.png"}],tab2:[{pagePath:"/pages/home/<USER>",text:"首页",iconPath:"/static/images/home.png",selectedIconPath:"/static/images/home_select.png"},{pagePath:"/pages/work/work",text:"工作台",iconPath:"/static/images/serve.png",selectedIconPath:"/static/images/serve_select.png"},{pagePath:"/pages/contacts/contacts",text:"通讯录",iconPath:"/static/images/contact.png",selectedIconPath:"/static/images/contact_select.png"},{pagePath:"/pages/my/my",text:"我的",iconPath:"/static/images/my.png",selectedIconPath:"/static/images/my_select.png"}]},D_=T_("tabBer",{state:()=>({role:"tab2",role1:""}),getters:{tabBarList:e=>L_[e.role]},unistorage:!0}),z_=T_("main",{state:()=>({hasLogin:!1,loginProvider:"",colorIndex:I_.colorIndex,colorList:I_.colorList,h5_wx_appId:"",h5_wx_redirect_url:"",h5_dd_appId:"",h5_dd_redirect_url:"",title:""}),actions:{login(e){this.hasLogin=!0,this.loginProvider=e},logout(){this.hasLogin=!1,this.loginProvider="";B_().clearAuthInfo()},setStore(e){const t=O_();B_().$patch({refreshToken:e.data.refreshToken,tokenType:e.data.tokenType,value:e.data.value,expiration:e.data.expiration}),t.$patch((t=>{t.currentOrgId=e.data.additionalInformation.currentOrgId,t.currentOrgName=e.data.additionalInformation.currentOrgName,t.staffName=e.data.additionalInformation.staffName,t.userJobDetailVOList=e.data.additionalInformation.userJobDetailVOList,t.userid=e.data.additionalInformation.userid,t.authorityList=e.data.additionalInformation.authorityList}))}},unistorage:!0}),R_={__name:"App",setup:e=>(B_(),z_(),o_((()=>{})),n_((()=>{})),()=>{})};Im(R_,{init:Bm,setup(e){const t=Wg(),n=()=>{var n;n=e,Object.keys(If).forEach((e=>{If[e].forEach((t=>{zo(e,t,n)}))}));const{onLaunch:o,onShow:i,onPageNotFound:r,onError:a}=e,s=function({path:e,query:t}){return c(Pp,{path:e,query:t}),c(Bp,Pp),c({},Pp)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Ae(t.query)});if(o&&D(o,s),i&&D(i,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};r&&D(r,e)}a&&(e.appContext.config.errorHandler=e=>{D(a,e)})};return eo(ul).isReady().then(n),jo((()=>{window.addEventListener("resize",Ce(Lm,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Dm),document.addEventListener("visibilitychange",zm),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{ow.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>($i(),Ki(Wb));e.setup=(e,o)=>{const i=t&&t(e,o);return v(i)?n:i},e.render=n}});const N_={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u:()=>uni.$u.deepMerge(uni.$u,{props:void 0,http:void 0,mixin:void 0}),bem:()=>function(e,t,n){const o=`u-${e}--`,i={};return t&&t.map((e=>{i[o+this[e]]=!0})),n&&n.map((e=>{this[e]?i[o+e]=this[e]:delete i[o+e]})),Object.keys(i)}},methods:{openPage(e="url"){const t=this[e];t&&this.$u.route({type:this.linkType,url:t})},$uGetRect(e,t){return new Promise((n=>{kf().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=uni.$u.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){Wd("uOnReachBottom")},beforeDestroy(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},j_={},{toString:F_}=Object.prototype;function q_(e){return"[object Array]"===F_.call(e)}function V_(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),q_(e))for(let n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function H_(){const e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=H_(e[n],t):e[n]="object"==typeof t?H_({},t):t}for(let n=0,o=arguments.length;n<o;n++)V_(arguments[n],t);return e}function Q_(e){return void 0===e}function U_(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function W_(e,t){if(!t)return e;let n;if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)n=t.toString();else{const e=[];V_(t,((t,n)=>{null!=t&&(q_(t)?n=`${n}[]`:t=[t],V_(t,(t=>{!function(e){return"[object Date]"===F_.call(e)}(t)?function(e){return null!==e&&"object"==typeof e}(t)&&(t=JSON.stringify(t)):t=t.toISOString(),e.push(`${U_(n)}=${U_(t)}`)})))})),n=e.join("&")}var o;if(n){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}const $_=(e,t)=>{const n={};return e.forEach((e=>{Q_(t[e])||(n[e]=t[e])})),n},X_=e=>(e=>new Promise(((t,n)=>{const o=W_((i=e.baseURL,r=e.url,i&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)?function(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}(i,r):r),e.params);var i,r;const a={url:o,header:e.header,complete:i=>{e.fullPath=o,i.config=e;try{"string"==typeof i.data&&(i.data=JSON.parse(i.data))}catch(r){}!function(e,t,n){const{validateStatus:o}=n.config,i=n.statusCode;!i||o&&!o(i)?t(n):e(n)}(t,n,i)}};let s;if("UPLOAD"===e.method){delete a.header["content-type"],delete a.header["Content-Type"];const t={filePath:e.filePath,name:e.name},n=["files","file","timeout","formData"];s=Ay({...a,...t,...$_(n,e)})}else if("DOWNLOAD"===e.method)Q_(e.timeout)||(a.timeout=e.timeout),s=xy(a);else{const t=["data","method","timeout","dataType","responseType","withCredentials"];s=vy({...a,...$_(t,e)})}e.getTask&&e.getTask(s,e)})))(e);function Y_(){this.handlers=[]}Y_.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Y_.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Y_.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const J_=(e,t,n)=>{const o={};return e.forEach((e=>{Q_(n[e])?Q_(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},G_={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,withCredentials:!1,validateStatus:function(e){return e>=200&&e<300}};var K_=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(s){t=function(){}}try{n=Set}catch(s){n=function(){}}try{o=Promise}catch(s){o=function(){}}function i(r,s,l,c,u){"object"==typeof s&&(l=s.depth,c=s.prototype,u=s.includeNonEnumerable,s=s.circular);var d=[],f=[],p="undefined"!=typeof Buffer;return void 0===s&&(s=!0),void 0===l&&(l=1/0),function r(l,h){if(null===l)return null;if(0===h)return l;var g,m;if("object"!=typeof l)return l;if(e(l,t))g=new t;else if(e(l,n))g=new n;else if(e(l,o))g=new o((function(e,t){l.then((function(t){e(r(t,h-1))}),(function(e){t(r(e,h-1))}))}));else if(i.__isArray(l))g=[];else if(i.__isRegExp(l))g=new RegExp(l.source,a(l)),l.lastIndex&&(g.lastIndex=l.lastIndex);else if(i.__isDate(l))g=new Date(l.getTime());else{if(p&&Buffer.isBuffer(l))return Buffer.from?g=Buffer.from(l):(g=new Buffer(l.length),l.copy(g)),g;e(l,Error)?g=Object.create(l):void 0===c?(m=Object.getPrototypeOf(l),g=Object.create(m)):(g=Object.create(c),m=c)}if(s){var v=d.indexOf(l);if(-1!=v)return f[v];d.push(l),f.push(g)}for(var y in e(l,t)&&l.forEach((function(e,t){var n=r(t,h-1),o=r(e,h-1);g.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=r(e,h-1);g.add(t)})),l){Object.getOwnPropertyDescriptor(l,y)&&(g[y]=r(l[y],h-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,y).set)continue;g[y]=r(l[y],h-1)}catch(S){if(S instanceof TypeError)continue;if(S instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(y=0;y<b.length;y++){var w=b[y];(!(_=Object.getOwnPropertyDescriptor(l,w))||_.enumerable||u)&&(g[w]=r(l[w],h-1),Object.defineProperty(g,w,_))}}if(u){var x=Object.getOwnPropertyNames(l);for(y=0;y<x.length;y++){var _,A=x[y];(_=Object.getOwnPropertyDescriptor(l,A))&&_.enumerable||(g[A]=r(l[A],h-1),Object.defineProperty(g,A,_))}}return g}(r,l)}function r(e){return Object.prototype.toString.call(e)}function a(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return i.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},i.__objToStr=r,i.__isDate=function(e){return"object"==typeof e&&"[object Date]"===r(e)},i.__isArray=function(e){return"object"==typeof e&&"[object Array]"===r(e)},i.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===r(e)},i.__getRegExpFlags=a,i}();const Z_=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+`&${n}`):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepMerge(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==uni.$u.page())if(t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:i,animationDuration:r}=e;"navigateTo"!=e.type&&"to"!=e.type||Vy({url:t,animationType:i,animationDuration:r}),"redirectTo"!=e.type&&"redirect"!=e.type||Hy({url:t}),"switchTab"!=e.type&&"tab"!=e.type||Wy({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||Qy({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Fy({delta:o})}}).route;function eA(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function tA(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}const nA={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=eA(e,!1),i=o[0],r=o[1],a=o[2],s=eA(t,!1),l=(s[0]-i)/n,c=(s[1]-r)/n,u=(s[2]-a)/n,d=[];for(let f=0;f<n;f++){let o=tA(`rgb(${Math.round(l*f+i)},${Math.round(c*f+r)},${Math.round(u*f+a)})`);0===f&&(o=tA(e)),f===n-1&&(o=tA(t)),d.push(o)}return d},hexToRgb:eA,rgbToHex:tA,colorToRgba:function(e,t){e=tA(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n}};function oA(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function iA(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function rA(e){return"[object Object]"===Object.prototype.toString.call(e)}function aA(e){return"function"==typeof e}const sA={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},date:function(e){return!!e&&(oA(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:oA,digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:iA,isEmpty:iA,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(t){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:rA,array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},func:aA,promise:function(e){return rA(e)&&aA(e.then)&&aA(e.catch)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)},image:function(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e}};let lA,cA=null;function uA(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function dA(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function fA(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=dA(e);return t>0?uA(Number(e)*Math.pow(10,t)):Number(e)}function pA(e){e>Number.MAX_SAFE_INTEGER||Number.MIN_SAFE_INTEGER}function hA(e,t){const[n,o,...i]=e;let r=t(n,o);return i.forEach((e=>{r=t(r,e)})),r}function gA(...e){if(e.length>2)return hA(e,gA);const[t,n]=e,o=fA(t),i=fA(n),r=dA(t)+dA(n),a=o*i;return pA(a),a/Math.pow(10,r)}function mA(...e){if(e.length>2)return hA(e,mA);const[t,n]=e,o=fA(t),i=fA(n);return pA(o),pA(i),gA(o/i,uA(Math.pow(10,dA(n)-dA(t))))}function vA(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;const t=sA.array(e)?[]:{};for(const n in e)e.hasOwnProperty(n)&&(t[n]="object"==typeof e[n]?vA(e[n]):e[n]);return t}function yA(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):new Date("string"==typeof e?e.replace(/-/g,"/"):e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const i in o){const[e]=new RegExp(`${i}+`).exec(t)||[];if(e){const n="y"===i&&2===e.length?2:0;t=t.replace(e,o[i].slice(n))}}return t}function bA(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let i=Math.ceil(o/t.length);for(;i>>=1;)t+=t,1===i&&(t+=t);return t.slice(0,o)+n});const wA={range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},getPx:function(e,t=!1){return sA.number(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${Vd(parseInt(e))}px`:Number(Vd(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},os:function(){return _v().platform.toLowerCase()},sys:function(){return _v()},random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),i=[];if(n=n||o.length,e)for(let r=0;r<e;r++)i[r]=o[0|Math.random()*n];else{let e;i[8]=i[13]=i[18]=i[23]="-",i[14]="4";for(let t=0;t<36;t++)i[t]||(e=0|16*Math.random(),i[t]=o[19==t?3&e|8:e])}return t?(i.shift(),`u${i.join("")}`):i.join("")},$parent:function(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addStyle:function(e,t="object"){if(sA.empty(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=bA(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[bA(o[0])]=bA(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return bA(n)},addUnit:function(e="auto",t=""){return t||(t=uni.$u.config.unit||"px"),e=String(e),sA.number(e)?`${e}${t}`:e},deepClone:vA,deepMerge:function e(t={},n={}){if("object"!=typeof(t=vA(t))||"object"!=typeof n)return!1;for(const o in n)n.hasOwnProperty(o)&&(o in t?null==n[o]||"object"!=typeof t[o]||"object"!=typeof n[o]?t[o]=n[o]:t[o].concat&&n[o].concat?t[o]=t[o].concat(n[o]):t[o]=e(t[o],n[o]):t[o]=n[o]);return t},error:function(e){},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},timeFormat:yA,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:yA(e,t)}return o},trim:bA,queryParams:function(e={},t=!0,n="brackets"){const o=t?"?":"",i=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const r in e){const t=e[r];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)i.push(`${r}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{i.push(`${r}[]=${e}`)}));break;case"repeat":t.forEach((e=>{i.push(`${r}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),i.push(`${r}=${e}`)}else i.push(`${r}=${t}`)}return i.length?o+i.join("&"):""},toast:function(e,t=2e3){pb({title:String(e),icon:"none",duration:t})},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const i=isFinite(+e)?+e:0,r=isFinite(+t)?Math.abs(t):0,a=void 0===o?",":o,s=void 0===n?".":n;let l="";l=(r?function(e,t){const n=Math.pow(10,t);let o=mA(Math.round(Math.abs(gA(e,n))),n);return e<0&&0!==o&&(o=gA(o,-1)),o}(i,r)+"":`${Math.round(i)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${a}$2`);return(l[1]||"").length<r&&(l[1]=l[1]||"",l[1]+=new Array(r-l[1].length+1).join("0")),l.join(s)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},padZero:function(e){return`00${e}`.slice(-2)},formValidate:function(e,t){const n=uni.$u.$parent.call(e,"u-form-item"),o=uni.$u.$parent.call(e,"u-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const i=t[0];e[i]&&"object"==typeof e[i]||(e[i]={}),t.shift(),o(e[i],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const i=t.split(".");o(e,i,n)}else e[t]=n},page:function(){const e=rm();return`/${e[e.length-1].route||""}`},pages:function(){return rm()},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:i}=uni.$u;uni.$u.config=i(uni.$u.config,t),uni.$u.props=i(uni.$u.props,e),uni.$u.color=i(uni.$u.color,n),uni.$u.zIndex=i(uni.$u.zIndex,o)}},xA={v:"3",version:"3",type:["primary","success","info","error","warning"],color:{"u-primary":"#2979ff","u-warning":"#ff9900","u-success":"#19be6b","u-error":"#fa3534","u-info":"#909399","u-main-color":"#303133","u-content-color":"#606266","u-tips-color":"#909399","u-light-color":"#c0c4cc"},unit:"px"},_A={calendar:{title:"日期选择",showTitle:!0,showSubtitle:!0,mode:"single",startText:"开始",endText:"结束",customList:()=>[],color:"#3c9cff",minDate:0,maxDate:0,defaultDate:null,maxCount:Number.MAX_SAFE_INTEGER,rowHeight:56,formatter:null,showLunar:!1,showMark:!0,confirmText:"确定",confirmDisabledText:"确定",show:!1,closeOnClickOverlay:!1,readonly:!1,showConfirm:!0,maxRange:Number.MAX_SAFE_INTEGER,rangePrompt:"",showRangePrompt:!0,allowSameDay:!1,round:0,monthNum:3}},AA={datetimePicker:{show:!1,popupMode:"bottom",showToolbar:!0,value:"",title:"",mode:"datetime",maxDate:new Date((new Date).getFullYear()+10,0,1).getTime(),minDate:new Date((new Date).getFullYear()-10,0,1).getTime(),minHour:0,maxHour:23,minMinute:0,maxMinute:59,filter:null,formatter:null,loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,closeOnClickOverlay:!1,defaultIndex:()=>[]}},{color:SA}=xA,TA={icon:{name:"",color:SA["u-content-color"],size:"16px",bold:!1,index:"",hoverClass:"",customPrefix:"uicon",label:"",labelPos:"right",labelSize:"15px",labelColor:SA["u-content-color"],space:"3px",imgMode:"",width:"",height:"",top:0,stop:!1}},{color:CA}=xA,EA={link:{color:CA["u-primary"],fontSize:15,underLine:!1,href:"",mpTips:"链接已复制，请在浏览器打开",lineColor:"",text:""}},{color:kA}=xA,PA={primary:"#3c9cff",info:"#909399",default:"#909399",warning:"#f9ae3d",error:"#f56c6c",success:"#5ac725",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},BA={actionSheet:{show:!1,title:"",description:"",actions:()=>[],index:"",cancelText:"",closeOnClickAction:!0,safeAreaInsetBottom:!0,openType:"",closeOnClickOverlay:!0,round:0},album:{urls:()=>[],keyName:"",singleSize:180,multipleSize:70,space:6,singleMode:"scaleToFill",multipleMode:"aspectFill",maxCount:9,previewFullImage:!0,rowCount:3,showMore:!0},alert:{title:"",type:"warning",description:"",closable:!1,showIcon:!1,effect:"light",center:!1,fontSize:14},avatar:{src:"",shape:"circle",size:40,mode:"scaleToFill",text:"",bgColor:"#c0c4cc",color:"#ffffff",fontSize:18,icon:"",mpAvatar:!1,randomBgColor:!1,defaultUrl:"",colorIndex:"",name:""},avatarGroup:{urls:()=>[],maxCount:5,shape:"circle",mode:"scaleToFill",showMore:!0,size:40,keyName:"",gap:.5,extraValue:0},backtop:{mode:"circle",icon:"arrow-upward",text:"",duration:100,scrollTop:0,top:400,bottom:100,right:20,zIndex:9,iconStyle:()=>({color:"#909399",fontSize:"19px"})},badge:{isDot:!1,value:"",show:!0,max:999,type:"error",showZero:!1,bgColor:null,color:null,shape:"circle",numberType:"overflow",offset:()=>[],inverted:!1,absolute:!1},button:{hairline:!1,type:"info",size:"normal",shape:"square",plain:!1,disabled:!1,loading:!1,loadingText:"",loadingMode:"spinner",loadingSize:15,openType:"",formType:"",appParameter:"",hoverStopPropagation:!0,lang:"en",sessionFrom:"",sendMessageTitle:"",sendMessagePath:"",sendMessageImg:"",showMessageCard:!1,dataName:"",throttleTime:0,hoverStartTime:0,hoverStayTime:200,text:"",icon:"",iconColor:"",color:""},..._A,carKeyboard:{random:!1},cell:{customClass:"",title:"",label:"",value:"",icon:"",disabled:!1,border:!0,center:!1,url:"",linkType:"navigateTo",clickable:!1,isLink:!1,required:!1,arrowDirection:"",iconStyle:{},rightIconStyle:{},rightIcon:"arrow-right",titleStyle:{},size:"",stop:!0,name:""},cellGroup:{title:"",border:!0,customStyle:{}},checkbox:{name:"",shape:"",size:"",checkbox:!1,disabled:"",activeColor:"",inactiveColor:"",iconSize:"",iconColor:"",label:"",labelSize:"",labelColor:"",labelDisabled:""},checkboxGroup:{name:"",value:()=>[],shape:"square",disabled:!1,activeColor:"#2979ff",inactiveColor:"#c8c9cc",size:18,placement:"row",labelSize:14,labelColor:"#303133",labelDisabled:!1,iconColor:"#ffffff",iconSize:12,iconPlacement:"left",borderBottom:!1},circleProgress:{percentage:30},code:{seconds:60,startText:"获取验证码",changeText:"X秒重新获取",endText:"重新获取",keepRunning:!1,uniqueKey:""},codeInput:{adjustPosition:!0,maxlength:6,dot:!1,mode:"box",hairline:!1,space:10,value:"",focus:!1,bold:!1,color:"#606266",fontSize:18,size:35,disabledKeyboard:!1,borderColor:"#c9cacc",disabledDot:!0},col:{span:12,offset:0,justify:"start",align:"stretch",textAlign:"left"},collapse:{value:null,accordion:!1,border:!0},collapseItem:{title:"",value:"",label:"",disabled:!1,isLink:!0,clickable:!0,border:!0,align:"left",name:"",icon:"",duration:300},columnNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80,step:!1,duration:1500,disableTouch:!0},countDown:{time:0,format:"HH:mm:ss",autoStart:!0,millisecond:!1},countTo:{startVal:0,endVal:0,duration:2e3,autoplay:!0,decimals:0,useEasing:!0,decimal:".",color:"#606266",fontSize:22,bold:!1,separator:""},...AA,divider:{dashed:!1,hairline:!0,dot:!1,textPosition:"center",text:"",textSize:14,textColor:"#909399",lineColor:"#dcdfe6"},empty:{icon:"",text:"",textColor:"#c0c4cc",textSize:14,iconColor:"#c0c4cc",iconSize:90,mode:"data",width:160,height:160,show:!0,marginTop:0},form:{model:()=>({}),rules:()=>({}),errorType:"message",borderBottom:!0,labelPosition:"left",labelWidth:45,labelAlign:"left",labelStyle:()=>({})},formItem:{label:"",prop:"",borderBottom:"",labelWidth:"",rightIcon:"",leftIcon:"",required:!1,leftIconStyle:""},gap:{bgColor:"transparent",height:20,marginTop:0,marginBottom:0,customStyle:{}},grid:{col:3,border:!1,align:"left"},gridItem:{name:null,bgColor:"transparent"},...TA,image:{src:"",mode:"aspectFill",width:"300",height:"225",shape:"square",radius:0,lazyLoad:!0,showMenuByLongpress:!0,loadingIcon:"photo",errorIcon:"error-circle",showLoading:!0,showError:!0,fade:!0,webp:!1,duration:500,bgColor:"#f3f4f6"},indexAnchor:{text:"",color:"#606266",size:14,bgColor:"#dedede",height:32},indexList:{inactiveColor:"#606266",activeColor:"#5677fc",indexList:()=>[],sticky:!0,customNavHeight:0},input:{value:"",type:"text",fixed:!1,disabled:!1,disabledColor:"#f5f7fa",clearable:!1,password:!1,maxlength:-1,placeholder:null,placeholderClass:"input-placeholder",placeholderStyle:"color: #c0c4cc",showWordLimit:!1,confirmType:"done",confirmHold:!1,holdKeyboard:!1,focus:!1,autoBlur:!1,disableDefaultPadding:!1,cursor:-1,cursorSpacing:30,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,inputAlign:"left",fontSize:"15px",color:"#303133",prefixIcon:"",prefixIconStyle:"",suffixIcon:"",suffixIconStyle:"",border:"surround",readonly:!1,shape:"square",formatter:null},keyboard:{mode:"number",dotDisabled:!1,tooltip:!0,showTips:!0,tips:"",showCancel:!0,showConfirm:!0,random:!1,safeAreaInsetBottom:!0,closeOnClickOverlay:!0,show:!1,overlay:!0,zIndex:10075,cancelText:"取消",confirmText:"确定",autoChange:!1},line:{color:"#d6d7d9",length:"100%",direction:"row",hairline:!0,margin:0,dashed:!1},lineProgress:{activeColor:"#19be6b",inactiveColor:"#ececec",percentage:0,showText:!0,height:12},...EA,list:{showScrollbar:!1,lowerThreshold:50,upperThreshold:0,scrollTop:0,offsetAccuracy:10,enableFlex:!1,pagingEnabled:!1,scrollable:!0,scrollIntoView:"",scrollWithAnimation:!1,enableBackToTop:!1,height:0,width:0,preLoadScreen:1},listItem:{anchor:""},...{loadingIcon:{show:!0,color:kA["u-tips-color"],textColor:kA["u-tips-color"],vertical:!1,mode:"spinner",size:24,textSize:15,text:"",timingFunction:"ease-in-out",duration:1200,inactiveColor:""}},loadingPage:{loadingText:"正在加载",image:"",loadingMode:"circle",loading:!1,bgColor:"#ffffff",color:"#C8C8C8",fontSize:19,iconSize:28,loadingColor:"#C8C8C8"},loadmore:{status:"loadmore",bgColor:"transparent",icon:!0,fontSize:14,iconSize:17,color:"#606266",loadingIcon:"spinner",loadmoreText:"加载更多",loadingText:"正在加载...",nomoreText:"没有更多了",isDot:!1,iconColor:"#b7b7b7",marginTop:10,marginBottom:10,height:"auto",line:!1,lineColor:"#E6E8EB",dashed:!1},modal:{show:!1,title:"",content:"",confirmText:"确认",cancelText:"取消",showConfirmButton:!0,showCancelButton:!1,confirmColor:"#2979ff",cancelColor:"#606266",buttonReverse:!1,zoom:!0,asyncClose:!1,closeOnClickOverlay:!1,negativeTop:0,width:"650rpx",confirmButtonShape:""},...{navbar:{safeAreaInsetTop:!0,placeholder:!1,fixed:!0,border:!1,leftIcon:"arrow-left",leftText:"",rightText:"",rightIcon:"",title:"",bgColor:"#ffffff",titleWidth:"400rpx",height:"44px",leftIconSize:20,leftIconColor:PA.mainColor,autoBack:!1,titleStyle:""}},noNetwork:{tips:"哎呀，网络信号丢失",zIndex:"",image:"data:image/png;base64,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"},noticeBar:{text:()=>[],direction:"row",step:!1,icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",speed:80,fontSize:14,duration:2e3,disableTouch:!0,url:"",linkType:"navigateTo"},notify:{top:0,type:"primary",color:"#ffffff",bgColor:"",message:"",duration:3e3,fontSize:15,safeAreaInsetTop:!1},...{numberBox:{name:"",value:0,min:1,max:Number.MAX_SAFE_INTEGER,step:1,integer:!1,disabled:!1,disabledInput:!1,asyncChange:!1,inputWidth:35,showMinus:!0,showPlus:!0,decimalLength:null,longPress:!0,color:"#323233",buttonSize:30,bgColor:"#EBECEE",cursorSpacing:100,disableMinus:!1,disablePlus:!1,iconStyle:""}},numberKeyboard:{mode:"number",dotDisabled:!1,random:!1},overlay:{show:!1,zIndex:10070,duration:300,opacity:.5},parse:{copyLink:!0,errorImg:"",lazyLoad:!1,loadingImg:"",pauseVideo:!0,previewImg:!0,setTitle:!0,showImgMenu:!0},picker:{show:!1,popupMode:"bottom",showToolbar:!0,title:"",columns:()=>[],loading:!1,itemHeight:44,cancelText:"取消",confirmText:"确定",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,keyName:"text",closeOnClickOverlay:!1,defaultIndex:()=>[],immediateChange:!1},popup:{show:!1,overlay:!0,mode:"bottom",duration:300,closeable:!1,overlayStyle:()=>{},closeOnClickOverlay:!0,zIndex:10075,safeAreaInsetBottom:!0,safeAreaInsetTop:!1,closeIconPos:"top-right",round:0,zoom:!0,bgColor:"",overlayOpacity:.5},radio:{name:"",shape:"",disabled:"",labelDisabled:"",activeColor:"",inactiveColor:"",iconSize:"",labelSize:"",label:"",labelColor:"",size:"",iconColor:"",placement:""},radioGroup:{value:"",disabled:!1,shape:"circle",activeColor:"#2979ff",inactiveColor:"#c8c9cc",name:"",size:18,placement:"row",label:"",labelColor:"#303133",labelSize:14,labelDisabled:!1,iconColor:"#ffffff",iconSize:12,borderBottom:!1,iconPlacement:"left"},rate:{value:1,count:5,disabled:!1,size:18,inactiveColor:"#b2b2b2",activeColor:"#FA3534",gutter:4,minCount:1,allowHalf:!1,activeIcon:"star-fill",inactiveIcon:"star",touchable:!0},readMore:{showHeight:400,toggle:!1,closeText:"展开阅读全文",openText:"收起",color:"#2979ff",fontSize:14,textIndent:"2em",name:""},row:{gutter:0,justify:"start",align:"center"},rowNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80},scrollList:{indicatorWidth:50,indicatorBarWidth:20,indicator:!0,indicatorColor:"#f2f2f2",indicatorActiveColor:"#3c9cff",indicatorStyle:""},search:{shape:"round",bgColor:"#f2f2f2",placeholder:"请输入关键字",clearabled:!0,focus:!1,showAction:!0,actionStyle:()=>({}),actionText:"搜索",inputAlign:"left",inputStyle:()=>({}),disabled:!1,borderColor:"transparent",searchIconColor:"#909399",searchIconSize:22,color:"#606266",placeholderColor:"#909399",searchIcon:"search",margin:"0",animation:!1,value:"",maxlength:"-1",height:32,label:null},section:{title:"",subTitle:"更多",right:!0,fontSize:15,bold:!0,color:"#303133",subColor:"#909399",showLine:!0,lineColor:"",arrow:!0},skeleton:{loading:!0,animate:!0,rows:0,rowsWidth:"100%",rowsHeight:18,title:!0,titleWidth:"50%",titleHeight:18,avatar:!1,avatarSize:32,avatarShape:"circle"},slider:{value:0,blockSize:18,min:0,max:100,step:1,activeColor:"#2979ff",inactiveColor:"#c0c4cc",blockColor:"#ffffff",showValue:!1,disabled:!1,blockStyle:()=>{}},statusBar:{bgColor:"transparent"},steps:{direction:"row",current:0,activeColor:"#3c9cff",inactiveColor:"#969799",activeIcon:"",inactiveIcon:"",dot:!1},stepsItem:{title:"",desc:"",iconSize:17,error:!1},sticky:{offsetTop:0,customNavHeight:0,disabled:!1,bgColor:"transparent",zIndex:"",index:""},subsection:{list:[],current:0,activeColor:"#3c9cff",inactiveColor:"#303133",mode:"button",fontSize:12,bold:!0,bgColor:"#eeeeef",keyName:"name"},swipeAction:{autoClose:!0},swipeActionItem:{show:!1,name:"",disabled:!1,threshold:20,autoClose:!0,options:[],duration:300},swiper:{list:()=>[],indicator:!1,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.35)",indicatorStyle:"",indicatorMode:"line",autoplay:!0,current:0,currentItemId:"",interval:3e3,duration:300,circular:!1,previousMargin:0,nextMargin:0,acceleration:!1,displayMultipleItems:1,easingFunction:"default",keyName:"url",imgMode:"aspectFill",height:130,bgColor:"#f3f4f6",radius:4,loading:!1,showTitle:!1},swiperIndicator:{length:0,current:0,indicatorActiveColor:"",indicatorInactiveColor:"",indicatorMode:"line"},switch:{loading:!1,disabled:!1,size:25,activeColor:"#2979ff",inactiveColor:"#ffffff",value:!1,activeValue:!0,inactiveValue:!1,asyncChange:!1,space:0},tabbar:{value:null,safeAreaInsetBottom:!0,border:!0,zIndex:1,activeColor:"#1989fa",inactiveColor:"#7d7e80",fixed:!0,placeholder:!0},tabbarItem:{name:null,icon:"",badge:null,dot:!1,text:"",badgeStyle:"top: 6px;right:2px;"},tabs:{duration:300,list:()=>[],lineColor:"#3c9cff",activeStyle:()=>({color:"#303133"}),inactiveStyle:()=>({color:"#606266"}),lineWidth:20,lineHeight:3,lineBgSize:"cover",itemStyle:()=>({height:"44px"}),scrollable:!0,current:0,keyName:"name"},tag:{type:"primary",disabled:!1,size:"medium",shape:"square",text:"",bgColor:"",color:"",borderColor:"",closeColor:"#C6C7CB",name:"",plainFill:!1,plain:!1,closable:!1,show:!0,icon:""},text:{type:"",show:!0,text:"",prefixIcon:"",suffixIcon:"",mode:"",href:"",format:"",call:!1,openType:"",bold:!1,block:!1,lines:"",color:"#303133",size:15,iconStyle:()=>({fontSize:"15px"}),decoration:"none",margin:0,lineHeight:"",align:"left",wordWrap:"normal"},textarea:{value:"",placeholder:"",placeholderClass:"textarea-placeholder",placeholderStyle:"color: #c0c4cc",height:70,confirmType:"done",disabled:!1,count:!1,focus:!1,autoHeight:!1,fixed:!1,cursorSpacing:0,cursor:"",showConfirmBar:!0,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,disableDefaultPadding:!1,holdKeyboard:!1,maxlength:140,border:"surround",formatter:null},toast:{zIndex:10090,loading:!1,text:"",icon:"",type:"",loadingMode:"",show:"",overlay:!1,position:"center",params:()=>{},duration:2e3,isTab:!1,url:"",callback:null,back:!1},toolbar:{show:!0,cancelText:"取消",confirmText:"确认",cancelColor:"#909193",confirmColor:"#3c9cff",title:""},tooltip:{text:"",copyText:"",size:14,color:"#606266",bgColor:"transparent",direction:"top",zIndex:10071,showCopy:!0,buttons:()=>[],overlay:!0,showToast:!0},transition:{show:!1,mode:"fade",duration:"300",timingFunction:"ease-out"},...{upload:{accept:"image",capture:()=>["album","camera"],compressed:!0,camera:"back",maxDuration:60,uploadIcon:"camera-fill",uploadIconColor:"#D3D4D6",useBeforeRead:!1,previewFullImage:!0,maxCount:52,disabled:!1,imageMode:"aspectFill",name:"",sizeType:()=>["original","compressed"],multiple:!1,deletable:!0,maxSize:Number.MAX_VALUE,fileList:()=>[],uploadText:"",width:80,height:80,previewImage:!0}}};let MA="none";MA="vue3",MA="h5";const IA={route:Z_,date:wA.timeFormat,colorGradient:nA.colorGradient,hexToRgb:nA.hexToRgb,rgbToHex:nA.rgbToHex,colorToRgba:nA.colorToRgba,test:sA,type:["primary","success","error","warning","info"],http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={}),this.config=K_({...G_,...e}),this.interceptors={request:new Y_,response:new Y_}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:H_(e.header||{},t.header||{})};if(o={...o,...J_(["getTask","validateStatus"],e,t)},"DOWNLOAD"===n)Q_(t.timeout)?Q_(e.timeout)||(o.timeout=e.timeout):o.timeout=t.timeout;else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","file","filePath","name","timeout","formData"].forEach((e=>{Q_(t[e])||(o[e]=t[e])})),Q_(o.timeout)&&!Q_(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","withCredentials"];o={...o,...J_(n,e,t)}}return o})(this.config,e);const t=[X_,void 0];let n=Promise.resolve(e);for(this.interceptors.request.forEach((e=>{t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}},config:xA,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==cA&&clearTimeout(cA),n){const n=!cA;cA=setTimeout((()=>{cA=null}),t),n&&"function"==typeof e&&e()}else cA=setTimeout((()=>{"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0){n?lA||(lA=!0,"function"==typeof e&&e(),setTimeout((()=>{lA=!1}),t)):lA||(lA=!0,setTimeout((()=>{lA=!1,"function"==typeof e&&e()}),t))},mixin:N_,mpMixin:j_,props:BA,...wA,color:PA,platform:"h5"};uni.$u=IA;const OA={install:e=>{e.config.globalProperties.$u=IA,e.config.globalProperties.$nextTick=e=>{e()},e.mixin(N_)}},LA={typeList:["JSON","STR","OBJ"],type:"STR",resetType(){this.type="STR"},changeType(e){this.type=this.typeList[e]},get(e,t=!1,n){let o=this;if(I_.storeSuffix&&(e=`${e}${I_.storeSuffix}`),!t)return new Promise(((t,i)=>{jv({key:e,success:function(e){switch(n||o.type){case"JSON":case"OBJ":t(JSON.parse(e.data));break;default:t(e.data)}},fail:function(e){i(null)}})}));try{const t=Nv(e);switch(n||o.type){case"JSON":case"OBJ":return JSON.parse(t);default:return t}}catch(i){return"fail"}},set(e,t,n=!1,o){let i=this;if(I_.storeSuffix&&(e=`${e}${I_.storeSuffix}`),!n)return new Promise(((n,r)=>{let a;switch(o||i.type){case"JSON":case"OBJ":default:a=JSON.stringify(t);break;case"STR":a=t}zv({key:e,data:a,success:function(e){n("success")},fail:function(e){r(null)}})}));{let n;switch(o||i.type){case"JSON":case"OBJ":default:n=JSON.stringify(t);break;case"STR":n=t}try{return Dv(e,n),"success"}catch(r){return"fail"}}},remove(e,t=!1){if(I_.storeSuffix&&(e=`${e}${I_.storeSuffix}`),!t)return new Promise(((t,n)=>{qv({key:e,success:function(e){t("success")},fail:function(){n(null)}})}));try{return Fv(e),"success"}catch(n){return"fail"}},clear(e=!1){if(!e)return new Promise(((e,t)=>{Hv(),e("success")}));try{return Vv(),"success"}catch(t){return"fail"}},has(e,t=!1){if(!t)return new Promise(((t,n)=>{let o=!1;jv({key:e,success:function(e){o=null!==e.data,t(o)},fail:function(){n(null)}})}));try{return!!Nv(e)}catch(n){return!1}}},DA=["/pages/index/index","/pages/login/login","/pages/generalPage/userAgreement/userAgreement","/pages/generalPage/privacyPolicy/privacyPolicy","/pages/generalPage/forgetPwd/forgetPwd","/pages/home/<USER>"];function zA(e){e=e.split("?")[0];let t=LA.get("hasLogin",!0);return t=Boolean(Number(t)),!(-1===DA.indexOf(e)&&!t)}function RA(){Qy({url:"/pages/login/login"})}Qd("navigateTo",{invoke:e=>!!zA(e.url)||(RA(),!1),success(e){}}),Qd("switchTab",{invoke:e=>!!zA(e.url)||(RA(),!1),success(e){}}),Qd("reLaunch",{invoke:e=>!!zA(e.url)||(RA(),!1),success(e){}});function NA(e={}){return function(t){{const{store:o,options:i}=t;let{unistorage:r}=i||{};if(!r)return;const{paths:a=null,afterRestore:s,beforeRestore:l,serializer:c={serialize:JSON.stringify,deserialize:JSON.parse},key:u=o.$id}=((e,t)=>{var n;return e="object"==typeof(n=e)&&null!==n?e:Object.create(null),new Proxy(e,{get:(e,n,o)=>Reflect.get(e,n,o)||Reflect.get(t,n,o)})})(r,e);null==l||l(t);try{const e=Nv(o.$id);e&&o.$patch(c.deserialize(e))}catch(n){}null==s||s(t),o.$subscribe(((e,t)=>{try{const e=Array.isArray(a)?function(e,t){return t.reduce(((t,n)=>{const o=n.split(".");return function(e,t,n){return t.slice(0,-1).reduce(((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{}),e)[t[t.length-1]]=n,e}(t,o,function(e,t){return t.reduce(((e,t)=>null==e?void 0:e[t]),e)}(e,o))}),{})}(t,a):t;Dv(u,c.serialize(e))}catch(n){}}),{detached:!0})}}}(function(){const e=Da(R_),t=m_();return t.use(NA()),e.use(t),e.use(OA),{app:e,Pinia:P_}})().app.use(xm).mount("#app");export{rg as $,I_ as A,rh as B,pg as C,T_ as D,s_ as E,qi as F,B_ as G,z_ as H,i_ as I,Qy as J,Rb as K,Vy as L,Ba as M,O_ as N,oo as O,an as P,pb as Q,gb as R,bg as S,mb as T,D_ as U,n_ as V,r_ as W,Hv as X,LA as Y,Nv as Z,a_ as _,N_ as a,Mf as a$,ob as a0,ag as a1,Eu as a2,Wy as a3,dg as a4,Er as a5,kb as a6,wg as a7,Bb as a8,l_ as a9,Ly as aA,Dy as aB,Py as aC,Xv as aD,my as aE,Jv as aF,hf as aG,Av as aH,gf as aI,Vp as aJ,hg as aK,Tb as aL,Lv as aM,gn as aN,tw as aO,vy as aP,Ay as aQ,In as aR,rm as aS,Qd as aT,jf as aU,Ff as aV,Dv as aW,Fv as aX,Vv as aY,Hy as aZ,Nf as a_,Qv as aa,Ah as ab,uv as ac,Fy as ad,kr as ae,_v as af,fr as ag,ni as ah,Jo as ai,zb as aj,xf as ak,Yd as al,hy as am,Fm as an,Pb as ao,Hm as ap,Cv as aq,kv as ar,Ev as as,ir as at,Eg as au,Mu as av,By as aw,Ey as ax,Iy as ay,Oy as az,Ko as b,Of as b0,Pf as b1,Bf as b2,jo as b3,zy as b4,jy as b5,qv as b6,$b as b7,Ki as c,BA as d,oi as e,rr as f,lr as g,Gi as h,ti as i,sr as j,kg as k,Ag as l,j_ as m,le as n,$i as o,vg as p,Wt as q,e_ as r,ce as s,Y as t,dn as u,kf as v,$n as w,sn as x,Xo as y,Ma as z};
