import{_ as s}from"./u--image.daf5e625.js";import{x as a,I as t,o as e,c as l,w as o,k as r,f as u,u as i,j as c,r as m,b as n,l as p,B as d}from"./index-4d380fda.js";import{_}from"./yuni-title.244fda8d.js";import{A as f,v as b}from"./staticUrl.6af4fd3e.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.5419d1e1.js";import"./u-icon.66912310.js";import"./u-transition.dd4c905d.js";const j=h({__name:"about",setup(h){const j=b;return a([]),a(""),t((s=>{})),(a,t)=>{const b=m(n("u--image"),s),h=r,x=m(n("yuni-title"),_),g=p,H=d;return e(),l(h,{class:"about"},{default:o((()=>[u(h,{class:"header-bgImage"},{default:o((()=>[u(b,{width:"750rpx",height:"812rpx",mode:"scaleToFill",src:i(f)},null,8,["src"])])),_:1}),u(h,{class:"header-title"},{default:o((()=>[u(x,{title:"关于我们",type:"short"})])),_:1}),u(h,{class:"about-content"},{default:o((()=>[u(h,{class:"about-content-text"},{default:o((()=>[u(h,{class:"aboutUs"},{default:o((()=>[u(g,{class:"aboutText"},{default:o((()=>[c("关于我们")])),_:1})])),_:1}),u(h,{class:"usText"},{default:o((()=>[c(" 统一门户移动端使用uniapp框架，集APP、微信小程序、钉钉H5微应用、钉钉小程序和H5等多端于一身，彻底实现一处开发，多处运行， 大量节省开发时间和人力成本。该框架也在不断的维护和优化中日渐完善，欢迎大家使用并提出有价值的修改建议，我们勠力同心，携手同行 迈向更高、更强、更安全。 ")])),_:1})])),_:1}),u(H,{class:"erweima",mode:"",src:i(j)},null,8,["src"])])),_:1}),u(h,{class:"share-images"}),u(h,{class:"aboutBah"},{default:o((()=>[c(" 备案号：TYMH123456789号 ")])),_:1})])),_:1})}}},[["__scopeId","data-v-5302619c"]]);export{j as default};
