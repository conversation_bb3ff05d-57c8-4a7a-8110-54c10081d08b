import{_ as a}from"./u-parse.f281f4ef.js";import{I as s,x as e,A as t,o as n,c as o,w as r,k as u,f as c,u as l,r as i,b as d}from"./index-4d380fda.js";import{d as p}from"./request.a87c83d2.js";import{_ as f}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uniUtils.7f6d2e72.js";const m=f({__name:"agreement",setup(f){s((a=>{}));var m=e();const _=t.tenantId_global;return async function(){const a=await p({configCodes:["yhxy"],clientType:"2",tenantId:_.value});m.value=a.data[0].configValue}(),(s,e)=>{const t=i(d("u-parse"),a),p=u;return n(),o(p,{class:"agreement"},{default:r((()=>[c(p,{class:"richText"},{default:r((()=>[c(t,{content:l(m)},null,8,["content"])])),_:1})])),_:1})}}},[["__scopeId","data-v-d3fdfd9b"]]);export{m as default};
