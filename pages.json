{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/home/<USER>",
      "scrollIndicator": "none",
      "app-plus": {
        "scrollIndicator": "none"
      },
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "uni-app"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/work/work",
      "style": {
        "navigationBarTitleText": "工作台",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/contacts/contacts",
      "style": {
        "navigationBarTitleText": "通讯录",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/my/my",
      "style": {
        "navigationBarTitleText": "我的",
        "enablePullDownRefresh": false
       
      }
    }
  ],
  //分包加载配置，此配置为小程序的分包加载机制。
  "subPackages": [
    {
      "root": "pages/generalPage", //存放除tabbar以及常用页面之外的页面。
      "pages": [
        {
          "path": "forgetPwd/forgetPwd",
          "style": {
            "navigationBarTitleText": "找回密码",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "notifyDetail/notifyDetail",
          "style": {
            "navigationBarTitleText": "通知详情",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "notify/notify",
          "style": {
            "navigationBarTitleText": "公告列表页",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "notice/notice",
          "style": {
            "navigationBarTitleText": "公告列表页",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contactsList/contactsList",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "contactsDetail/contactsDetail",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "addressDetail/addressDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "about/about",
          "style": {
            "navigationBarTitleText": "关于我们",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "noauthority/noauthority",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "agreement/agreement",
          "style": {
            "navigationBarTitleText": "用户协议",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "privacy/privacy",
          "style": {
            "navigationBarTitleText": "隐私政策",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/UNI",
      "pages": [
        {
          "path": "webview/webview",
          "style": {
            "navigationBarTitleText": "默认页面",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "network/networkError",
          "style": {
            "navigationBarTitleText": "无网络连接",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "netError/netError",
          "style": {
            "navigationBarTitleText": "网络检测示例",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/comExample",
      "pages": [
        {
          "path": "textToVoice/textToVoice",
          "style": {
            "navigationBarTitleText": "文字转语音",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "tree/tree",
          "style": {
            "navigationBarTitleText": "组织树",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "comIndex/comIndex",
          "style": {
            "navigationBarTitleText": "组件示例",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "socket/socket",
          "style": {
            "navigationBarTitleText": "socket连接",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "socket/websocket",
          "style": {
            "navigationBarTitleText": "websocket",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "socket/socketTask",
          "style": {
            "navigationBarTitleText": "socketTask",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "file-download/file-download",
          "style": {
            "navigationBarTitleText": "图片上传下载",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "signature/signature",
          "style": {
            "navigationBarTitleText": "手写签名",
            "enablePullDownRefresh": false,
            "app-plus": {
              "bounce": "none"
            }
          }
        },
        {
          "path": "editRichText/editRichText",
          "style": {
            "navigationBarTitleText": "富文本编辑",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "debounceAndThrottle/debounceAndThrottle",
          "style": {
            "navigationBarTitleText": "防抖节流",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "mapDemo/map",
          "style": {
            "navigationBarTitleText": "地图",
            "enablePullDownRefresh": false
          }
        },
        // #ifdef MP-WEIXIN
        {
          "path": "mapDemo/mapDemo",
          "style": {
            "navigationBarTitleText": "地图demo",
            "enablePullDownRefresh": false
          }
        },
        // #endif
        {
          "path": "uchartsDemo/uchartsDemo",
          "style": {
            "navigationBarTitleText": "ucharts示例",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "bpmnDemo/bpmnDemo",
          "style": {
            "navigationBarTitleText": "流程化工具bpmn",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "tencentLocation/tencentLocation",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "xgplay/xgplay",
          "style": {
            "navigationBarTitleText": "西瓜播放器",
            "enablePullDownRefresh": false
          }
        },
        // #ifdef H5-DINGTALK
        {
          "path": "dingH5scan/dingH5scan",
          "style": {
            "navigationBarTitleText": "钉钉示例",
            "enablePullDownRefresh": false
          }
        },
        // #endif
        // #ifndef MP-WEIXIN
        {
          "path": "xgplayerDemo/xgplayerDemo",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        // #endif
        // #ifdef H5
        {
          "path": "gisDemo/gisDemo",
          "style": {
            "navigationBarTitleText": "gis示例",
            "enablePullDownRefresh": false
          }
        },
        // #endif
        {
          "path": "drawLock/drawLock",
          "style": {
            "navigationBarTitleText": "图形解锁",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "fileSelect/fileSelect",
          "style": {
            "navigationBarTitleText": "动态权限申请",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "trendsPermission/trendsPermission",
          "style": {
            "navigationBarTitleText": "动态权限专题",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/vehicle-management",
      "pages": [
         {
          "path": "parking-management/home",
          "style": {
            "navigationBarTitleText": "停车管理",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "parking-record/index",
          "style": {
            "navigationBarTitleText": "停车记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "parking-record/detail",
          "style": {
            "navigationBarTitleText": "停车记录详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "parking-order/index",
          "style": {
            "navigationBarTitleText": "停车订单",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "parking-order/detail",
          "style": {
            "navigationBarTitleText": "停车订单详情",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "parking-management/index",
          "style": {
            "navigationBarTitleText": "停车管理",
            "enablePullDownRefresh": true
          }
        },
         {
          "path": "parking-management/editCar",
          "style": {
            "navigationBarTitleText": "修改车辆信息",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "pages/paymentCenter",
      "pages": [
        {
          "path": "account-management/index",
          "style": {
            "navigationBarTitleText": "账户明细",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "account-management/list",
          "style": {
            "navigationBarTitleText": "账户列表",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "account-management/detail",
          "style": {
            "navigationBarTitleText": "查看流水信息",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "my-account-list/index",
          "style": {
            "navigationBarTitleText": "账户明细",
            "enablePullDownRefresh": false
          }
        },
 {
          "path": "account-order/index",
          "style": {
            "navigationBarTitleText": "账户顺序",
            "enablePullDownRefresh": false
          }
        }
       
      ]
    },
    {
      "root": "pages/userCenter",
      "pages": [
        {
          "path": "userCenter",
          "style": {
            "navigationBarTitleText": "用户中心",
            "enablePullDownRefresh": false
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#c20000",
    "backgroundColor": "#fff",
    "app-plus": {
      "scrollIndicator": "none", // 在APP平台都不显示滚动条
      "bounce": "none"
    }
  },
  "tabBar": {
    "color": "#000",
    "selectedColor": "#c20000",
    "list": [
      {
        "pagePath": "pages/home/<USER>"
      },
      {
        "pagePath": "pages/work/work"
      },
      {
        "pagePath": "pages/contacts/contacts"
      },
      {
        "pagePath": "pages/my/my"
      }
    ]
  }
}