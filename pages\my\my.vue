<template>
  <view class="my">
    <!-- <yuni-water-mark :watermark="watermarkName"></yuni-water-mark> -->
    <view class="header-backgroundImage">
      <u--image
        width="750rpx"
        height="300rpx"
        mode="scaleToFill"
        src="/static/images/home-title.png"
      ></u--image>
    </view>
    <view class="container">
      <view class="header-banner">
        <swiper
          class="swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="3000"
          :duration="1000"
          @change="topChange"
        >
          <swiper-item v-for="(item, index) in bannerList" :key="index">
            <view :class="item.colorClass" class="swiper-item">
              <image
                class="swiperImage"
                src="/static/images/my_banner.png"
                mode="scaleToFill"
              />
            </view>
          </swiper-item>
        </swiper>

        <view class="card">
          <view class="card-top">
            <view class="card-left">
              <image class="card-image" :src="oval_url" mode=""></image>
            </view>
            <view class="card-right">
              <view class="card-text">
                {{ userStore.userInfo.loginName }}
              </view>
              <view class="card-time">{{ userStore.userInfo.cellphone }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 统计概览 -->
      <view class="borad">
        <view class="borad-box">
          <view class="one-show">
            <view class="top-show-text">本月支出</view>
            <view class="bottom-info">
              <text class="icon iconfont icon-qian"></text>
              <text class="bottom-info-text">{{ info.payout }}</text>
            </view>
          </view>
          <view class="icon icon-fenges"></view>
          <view class="one-show">
            <view class="top-show-text">总金额</view>
            <view class="bottom-info">
              <text class="icon iconfont icon-qian"></text>
              <text class="bottom-info-text">{{ info.totalBalance }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 积分卡片 -->
      <view class="points-section" v-if="accountList.length > 0">
        <view class="section-title">我的账户</view>
        <view
          class="points-card"
          v-for="(account, index) in accountList"
          :key="index"
        >
          <view class="points-content">
            <view class="points-left">
              <view class="points-label">{{ account.name || '积分余额' }}</view>
              <view class="points-value">{{
                account.spendingBalance || '0.00'
              }}</view>
            </view>
            <view class="points-right">
              <view
                class="points-detail-btn"
                @click="goToPointsDetail(account)"
              >
                <text class="btn-text">账户明细</text>
                <uni-icons
                  type="arrowright"
                  size="14"
                  color="#ffffff"
                ></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="bottom">
        <view class="panel-list" v-for="(item, index) in listData" :key="index">
          <view class="panel-item">
            <view class="content" @click="onclick(item)">
              <view class="content-left">
                <image class="img1" :src="item.src" mode=""></image>
                <text class="content-left-text">{{ item.title }}</text>
              </view>
              <view class="content-right">
                <uni-icons class="arrowright" type="arrowright"></uni-icons>
              </view>
            </view>
          </view>

          <view v-if="item.flag" class="kb"> </view>
          <view v-else class="split"> </view>
        </view>
      </view>
      <uni-popup ref="showpopup" :type="type" @change="change">
        <view class="popup-content">
          <view class="select">
            <view class="uni-title uni-common-mt uni-common-pl">切换岗位</view>
            <view class="iconfont icon-close" @click="closePopup"></view>
          </view>
          <view class="uni-list">
            <radio-group @change="radioChange">
              <label
                class="uni-list-cell"
                v-for="(item, index) in userStore.userInfo.userJobDetailVOList"
                :key="item.orgId"
              >
                <radio
                  class="radio"
                  :value="index + ''"
                  :checked="index === current"
                />
                <view class="orgName">{{ item.orgName }}</view>
              </label>
            </radio-group>
          </view>
        </view>
      </uni-popup>
    </view>
    <yuni-tabbar :current="1"></yuni-tabbar>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onShow, onUnload } from '@dcloudio/uni-app'
import { useMainStore } from '@/store/index.js'
import { logout, switchJobTitle } from '@/common/api.js'
import { config } from '@/config/config.js'
import { accountInfo, statistic } from '@/common/api/paymentCenter/account-list'
// import pickerColor from '@/components/helang-pickerColor/helang-pickerColor.vue'
import {
  OVAL_URL,
  XZ1_URL,
  XZ2_URL,
  XZ3_URL,
  XZ4_URL,
  XZ5_URL,
  XZ6_URL,
  XZ7_URL,
  XZ8_URL,
  QIEHUANZUHU_URL,
  COMMON_MY_BACKGROUNDIMG_URL,
} from '@/common/net/staticUrl.js'

const bannerList = ref([{}])

// 积分账户数据
const accountList = ref([])
const formData = reactive({
  staffId: '',
})

// 统计数据
const info = reactive({
  payout: 0.0,
  totalBalance: 0.0,
})

// 水印
// import { useInfoStore } from '@/store/info.js'
// const infoStore = useInfoStore()
// const watermark = ref(infoStore.userInfo.loginName + ' ' + infoStore.userInfo.orgName)

const mainStore = useMainStore()
uni.hideTabBar()
import * as myDIng from 'dingtalk-jsapi'
const isNavBar = ref(config.isHideNavBar)

onShow(() => {
  if (isNavBar.value) {
    myDIng.biz.navigation.hideBar({
      hidden: true,
    })
  } else {
    // 钉钉H5设置顶部导航栏标题
    myDIng.biz.navigation.setTitle({
      title: '我的', //控制标题文本，空字符串表示显示默认文本
    })
  }
})

onMounted(async () => {
  uni.showLoading({
    title: '加载中...',
  })

  try {
    // 并行执行两个请求
    await Promise.all([getAccountInfo(), getStatisticInfo()])
  } finally {
    uni.hideLoading()
  }
})

const listData = ref([
  // {
  //   title: '关于我们',
  //   src: XZ3_URL,
  //   flag: true,
  //   type: 'aboutUs',
  // },
  {
    title: '切换岗位',
    src: '/static/images/shbm.png',
    flag: false,
    type: 'togglePopup',
  },

  {
    title: '用户中心',
    src: '/static/images/yhzx.png',
    flag: false,
    type: 'userCenter',
  },

  {
    title: '隐私政策',
    src: '/static/images/yglx.png',
    flag: false,
    type: 'onPrivacy',
  },
  {
    title: '用户协议',
    src: '/static/images/yglx.png',
    flag: false,
    type: 'onUserAgreement',
  },
  // {
  //   title: '主题切换',
  //   src: XZ5_URL,
  //   flag: false,
  //   type: 'showPickerColorPop',
  // },
  // {
  //   title: '示例案例',
  //   src: XZ6_URL,
  //   flag: false,
  //   type: 'onSample',
  // },
  // {
  //   title: '知识库',
  //   src: XZ7_URL,
  //   flag: true,
  //   type: 'goRepository',
  // },
  {
    title: '退出登录',
    src: '/static/images/tuichu.png',
    flag: true,
    type: 'logoutOfLogin',
  },
])
const oval_url = ref(OVAL_URL)
const qiehuanzuhu = ref(QIEHUANZUHU_URL)
const login_static_images = ref(COMMON_MY_BACKGROUNDIMG_URL)
// 弹出层位置
var type = ref('')
var title = ref('radio 单选框')
var current = ref(0)

// 控制选择主题显示与隐藏
var showPickerColor = ref(false)
var colorStart = ref(uni.getStorageSync('navBgColor', 'btnBgColor') + '')
var colorEnd = ref(uni.getStorageSync('navBgColor', 'btnBgColor') + '')
// 隐私政策跳转
import { joinUrlParams } from '@/common/uniUtils.js'

function onclick(item) {
  switch (item.type) {
    case 'onPrivacy':
      onPrivacy()
      break
    case 'onUserAgreement':
      onUserAgreement()
      break
    case 'aboutUs':
      aboutUs()
      break
    case 'togglePopup':
      togglePopup('center', 'popup')
      break
    case 'showPickerColorPop':
      showPickerColorPop()
      break
    case 'onSample':
      onSample()
      break
    case 'goRepository':
      goRepository()
      break
    case 'logoutOfLogin':
      logoutOfLogin()
      break
    case 'userCenter':
      userCenter()
      break
  }
}

function onPrivacy() {
  // #ifdef MP-WEIXIN
  // 微信小程序不支持web-view本地路径跳转 #TODO

  // #endif
  // #ifndef MP-WEIXIN
  // let url = joinUrlParams(
  //   '/pages/UNI/webview/webview',
  //   { title: '隐私政策', url: '/hybrid/html/privacy.html' },
  //   ['title', 'url']
  // )
  // uni.navigateTo({
  //   url,
  // })
  // #endif
  uni.navigateTo({
    url: '/pages/generalPage/privacy/privacy?navTitle=隐私政策',
  })
}
// 用户中心
function userCenter() {
  uni.navigateTo({
    url: '/pages/userCenter/userCenter',
  })
}

// 账户明细
function goToPointsDetail(account) {
  uni.navigateTo({
    url: `/pages/paymentCenter/my-account-list/index?accountListId=${account.id}&staffId=${account.staffId}`,
  })
}

// 获取账户信息
const getAccountInfo = async () => {
  try {
    const res = await accountInfo({
      staffId: userStore.userInfo.staffId,
    })

    if (res.success) {
      accountList.value = res.data.map((item) => ({
        ...item,
        name: item.accountManageName, // 将accountManageName赋值给name
      }))
    }
  } catch (error) {
    console.error('获取账户信息失败:', error)
  }
}

// 获取统计数据
const getStatisticInfo = async () => {
  try {
    const res = await statistic({
      staffId: userStore.userInfo.staffId,
    })

    if (res.success) {
      info.payout = res.data.payout
      info.totalBalance = res.data.totalBalance
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}
// 用户协议
function onUserAgreement() {
  // #ifdef MP-WEIXIN
  // 微信小程序不支持web-view本地路径跳转 #TODO

  // #endif
  // #ifndef MP-WEIXIN
  // let url = joinUrlParams(
  //   '/pages/UNI/webview/webview',
  //   { title: '用户协议', url: '/hybrid/html/agreement.html' },
  //   []
  // )
  // uni.navigateTo({
  //   url,
  // })
  // #endif
  uni.navigateTo({
    url: '/pages/generalPage/agreement/agreement?navTitle=用户协议',
  })
}
// 退出登录
import kvStore from '@/common/store/uniKVStore.js'
function logoutOfLogin() {
  uni.showModal({
    title: '',
    content: '确定要退出吗？',
    success: function (res) {
      if (res.confirm) {
        logout({})
          .then((res) => {
            console.log('logout', res)
            kvStore.clear()
            mainStore.logout()
            uni.reLaunch({
              url: '/pages/login/login',
            })
          })
          .catch((err) => {
            uni.showToast({
              title: err,
              icon: 'none',
              duration: 2000,
              success: () => {
                uni.reLaunch({
                  url: '/pages/login/login',
                })
              },
            })
          })
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    },
  })
}
// 关于我们
function aboutUs() {
  uni.navigateTo({
    url: '/pages/generalPage/about/about?navTitle=关于我们',
  })
}
//知识库跳转
function goRepository() {
  uni.navigateTo({
    url: '/pages/knowledge/knowledge',
  })
}
// 主题切换
//显示获取颜色选择弹窗
function showPickerColorPop() {
  showPickerColor.value = true
}
//获取颜色选择回调
function getPickerColor(color) {
  showPickerColor.value = false
  // self.setColorIndex(color.pickerIndex)
  uni.setStorageSync('colorIndex', color.pickerIndex)
  if (color.pickerColor) {
    //判断颜色值是否有效
    colorEnd.value = color.pickerColor
    showPickerColor.value = false
    uni.setStorageSync('navBgColor', color.pickerColor)
    let fontColor = '#ffffff'
    // self.filtrateColor(fontColor, color.pickerColor)
  }
  let params = {
    xuan: 'wangyuxuan',
    feng: 'daxiongmiao ',
    yu: [],
    hj: {},
  }
  // callbackPage(params)
}
function onClose() {
  const self = this
  let params = {
    xuan: 'wangyuxuan',
    feng: 'daxiongmiao ',
    yu: [],
    hj: {},
  }
  // callbackPage(params)
  closePage() //关闭当前页面
}
// 示例DEMO
function onSample() {
  uni.navigateTo({
    url: '/pages/comExample/comIndex/comIndex',
  })
}
// 点击切换岗位
function togglePopup(t, open) {
  switch (t) {
    case 'top':
      // this.content = '顶部弹出 popup'
      break

    case 'bottom':
      // this.content = '底部弹出 popup'
      break
    case 'center':
      // this.content = '居中弹出 popup'
      break
  }
  type.value = t
  showpopup.value.open()
}
function change(e) {}
function popupBtn() {}
// 切换岗位单选框
// 更新个人信息
import { useUserStore } from '@/store/user.js'
import { useTokenStore } from '@/store/token.js'
import { useTabBerStore } from '@/store/tabBer.js'
const userStore = useUserStore()
const tokenStore = useTokenStore()
const tabBerStore = useTabBerStore()
const showpopup = ref(null)
console.log(
  'userStore.userInfo.userJobDetailVOList',
  userStore.userInfo.userJobDetailVOList,
)
const userList = userStore.userInfo.userJobDetailVOList
function radioChange(evt) {
  uni.showLoading({
    title: '切换岗位中，请稍后...',
  })
  current.value = parseInt(evt.detail.value)
  let curJobInfo = userStore.userInfo.userJobDetailVOList[current.value]
  switchJobTitle({
    orgId: curJobInfo.orgId,
  }).then((result) => {
    console.log('result', result)
    if (result.data.expiration != '1711618193373') {
      tabBerStore.role = 'tab1'
      uni.reLaunch({
        url: '/pages/home/<USER>',
      })
    } else {
      tabBerStore.role = 'tab1'
      uni.reLaunch({
        url: '/pages/home/<USER>',
      })
    }
    mainStore.setStore(result)
    uni.hideLoading()
    showpopup.value.close()
  })
}
// 弹出层关闭按钮
function closePopup() {
  showpopup.value.close()
}
onUnload(() => {
  const self = this
  if (colorStart.value !== colorEnd.value) {
    // uni.reLaunch({
    //   url: '/pages/example/components',
    // })
  }
  let params = {
    xuan: 'wangyuxuan',
    feng: 'daxiongmiao ',
    yu: [],
    hj: {},
  }
  // callbackPage(params)
})
</script>

<style lang="scss" scoped>
.header-backgroundImage {
  width: 750rpx;
  background-size: cover;
  z-index: 100;
  position: absolute;
  top: -20rpx;
  :deep(.u-image) {
    height: 600rpx !important;
    div {
      border-bottom-left-radius: 40rpx !important;
      border-bottom-right-radius: 40rpx !important;
    }
  }
}

button {
  border: none;
}

button::after {
  border: none;
}

.my {
  width: 750rpx;
  height: 100vh;
  background-color: #f5f6f9;
}

.container {
  width: 750rpx;
  background-repeat: no-repeat;
}

.my-title {
  height: 45rpx;
  font-size: 32rpx;
  font-family: $uni-font-family;
  font-weight: 400;
  color: #ffffff;
  line-height: 45rpx;
  margin-left: 23rpx;
  /* #ifdef MP-WEIXIN */
  padding-top: 80rpx;
  /* #endif */
  /* #ifdef APP-PLUS */
  padding-top: 60rpx;
  /* #endif */
  /* #ifdef H5 */
  padding-top: 7rpx;
  /* #endif */
}

.card {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

.card-top {
  display: flex;
}

.card-left {
  width: 132rpx;
  height: 132rpx;
  margin-left: 35rpx;
}

.card-image {
  width: 100%;
  height: 100%;
  background-size: cover;
}

.card-right {
  display: flex;
  flex-direction: column;
  margin-left: 30rpx;
  margin-top: 10rpx;
}

/* 统计概览样式 */
.borad {
  display: flex;
  justify-content: center;

  background: #ffffff;
  border-radius: 0.5rem;
  margin: 0px 0.6875rem;
  margin-top: 0.6875rem;
  padding: 0.9375rem;
  padding-top: 0px;
}

.borad-box {
  margin-top: 24rpx;
  width: 100%;
  background: linear-gradient(to bottom, #fde9e5, #ffffff);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-fenges {
    height: 160rpx;
    background: #eb958a;
    width: 5rpx;
  }

  .one-show {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .top-show-text {
      color: #333333;
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }

    .bottom-info {
      margin-top: 20rpx;

      .icon {
        font-size: 45rpx;
        color: #333333;
        font-weight: bold;
        padding-right: 5rpx;
      }

      .bottom-info-text {
        font-size: 36rpx;
        color: #333333;
        font-weight: bold;
      }
    }
  }
}

/* 积分卡片样式 */
.points-section {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 0px 22rpx;
  margin-top: 22rpx;
  padding: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .points-card {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .points-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .points-left {
        flex: 1;

        .points-label {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 10rpx;
        }

        .points-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #ffffff;
          line-height: 1.2;
        }
      }

      .points-right {
        .points-detail-btn {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50rpx;
          padding: 16rpx 24rpx;
          display: flex;
          align-items: center;
          border: 1rpx solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;

          &:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.98);
          }

          .btn-text {
            font-size: 26rpx;
            color: #ffffff;
            margin-right: 8rpx;
          }
        }
      }
    }
  }
}

.bottom {
  margin-top: 40rpx;
  background: #ffffff;
  background: #ffffff;
  border-radius: 16rpx;
  margin: 0px 22rpx;
  margin-top: 22rpx;
}

.panel-item {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0px 34rpx;
  box-sizing: border-box;
  .content {
    display: flex;
    width: 100%;
    .content-left {
      display: flex;
      align-items: center;
      flex: 1;
      .img1 {
        width: 40rpx;
        height: 40rpx;
        margin-right: 27rpx;
        background-size: cover;
        position: relative;
        top: 2rpx;
      }

      .content-left-text {
        width: 150rpx;
        font-size: 32rpx;
        font-family: $uni-font-family;
        font-weight: 400;
        color: #333333;
      }
    }

    .content-right {
      .arrowright {
        width: 14rpx;
        height: 23rpx;
      }
    }
  }
}

.split {
  margin-left: 36rpx;
  width: 678rpx;
  height: 2rpx;
  background: #f4f4f4;
}

.kb {
  width: 750rpx;
  height: 20rpx;
  background: #f5f8fe;
}

.card-text {
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 48rpx;
}

.card-time {
  font-size: 26rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.77);
  line-height: 114rpx;
  max-width: 500rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn {
  width: 100%;
  height: 200rpx;
}

.logoutBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 630rpx;
  height: 100%;
  margin-top: 30rpx;
  line-height: 100rpx;
  font-size: 28rpx;
  color: #000;
  background: #f3f4f6;
  border-radius: 50rpx;
}

.popup-content {
  z-index: 9999;
  width: 600rpx;
  height: 488rpx;
  box-shadow: 0px 0px 5px 0px #cccccc;
  /* #ifndef APP-NVUE */
  display: block;
  /* #endif */
  background-color: #fff;
  padding-bottom: calc(var(--window-bottom) + 15rpx);
  font-size: 28rpx;
  border-radius: 30rpx;
  padding: 20rpx;

  .select {
    display: flex;
    justify-content: space-between;

    .iconfont {
      font-size: 50rpx;
    }
  }
}

.uni-title {
  font-size: 28rpx;
  font-family: $uni-font-family;
  font-weight: 600;
  color: #333333;
  line-height: 40rpx;
}

.pop {
  bottom: 86rpx;
}

.icon-close {
  margin-right: 30rpx;
}

.uni-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
}

.uni-list-cell {
  display: flex;
  align-items: center;
  margin-top: 20rpx;

  .orgName {
    height: 40rpx;
    font-size: 32rpx;
    font-family: $uni-font-family;
    font-weight: 400;
    color: #000000;
    line-height: 40rpx;
  }
}

.header-banner {
  height: 280rpx;
  margin: 0px 20rpx;
  margin-top: 20rpx;
  z-index: 200;
  position: relative;
  .swiper {
    height: 100% !important;
  }

  .swiper-item {
    height: 100% !important;
  }

  .swiperImage {
    width: 100%;
    height: 100% !important;
  }
}
:deep(.uni-swiper-dot-active) {
  background-color: #ffffff !important;
}
</style>
