<template>
  <view class="page-box">
    <dingtalkNavBar title="账户顺序" :hide-nav-bar="true"></dingtalkNavBar>
    <view class="pand-sort">
      <view class="sort-box">
        <view class="sort-title">
          <text class="text-1">将按以下顺序扣款（特殊场景除外）</text><text class="text-2">长按拖动</text>
        </view>

        <HM-dragSorts ref="dragSorts" :list="dataList" :autoScroll="true" :feedbackGenerator="true" :rowHeight="55"
          @change="change" @confirm="confirm" @onclick="onclick" @goMangentDetail="goMangentDetail"></HM-dragSorts>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue'
import { OVAL_URL } from '@/common/net/staticUrl.js'
import * as echarts from 'echarts'
import { statistic, accountInfo, updateSort } from '@/common/api/paymentCenter/account-list'
import { onPullDownRefresh } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user.js'
const userStore = useUserStore()
const oval_url = ref(OVAL_URL)
const formData = reactive({
  staffId: userStore.userInfo.staffId
})
const info = reactive({
  payout: 0.00,
  totalBalance: 0.00
})
const dataList = ref([


])

// 事件处理
const onclick = (e) => {
  console.log('=== onclick start ===')
  console.log('被点击行: ' + JSON.stringify(e.value))
  console.log('被点击下标: ' + JSON.stringify(e.index))
  console.log('=== onclick end ===')
}

const change = (e) => {
  console.log('=== change start ===')
  console.log('被拖动行: ' + JSON.stringify(e.moveRow))
  console.log('原始下标：', e.index)
  console.log('移动到：', e.moveTo)
  console.log('=== change end ===')

}

const confirm = (e) => {
  console.log('=== confirm start ===')
  console.log('被拖动行: ' + JSON.stringify(e.moveRow))
  console.log('原始下标：', e.index)
  console.log('移动到：', e.moveTo)
  console.log('=== confirm end ===')
  // 调用排序方法并传递参数
  sortFun({
    id: e.moveRow.id, // 假设拖动项有id字段
    unionId: e.moveRow.unionId, // 假设拖动项有unionId字段
    accountSort: e.moveTo // 使用新的排序位置
  })
}

const goMangentDetail = (e) => {
  const item = {
    id:e.id
  }
   uni.navigateTo({
    url: `/pages/paymentCenter/account-management/detail?item=${JSON.stringify(item)}`,
  })
}
// 修改后的排序方法
const sortFun = async (params) => {
  try {
    const res = await updateSort({
      ...params
    })
    if (res.success) {
      uni.showToast({
        title: '排序更新成功',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '排序更新失败',
      icon: 'none'
    })
    console.error('Update sort error:', error)
  }
}
// 获取数据
const getList = async () => {
  try {
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)

    const res = await accountInfo({
      ...formData,
    })

    if (res.success) {
      dataList.value = res.data.map(item => ({
        ...item,
        name: item.accountManageName // 将accountManageName赋值给name
      }))
    }
  } finally {
    uni.hideLoading()
  }
}
// 获取总览
const statisticQuery = async () => {
  setTimeout(function () {
    uni.hideLoading()
  }, 10000)
  try {
    const res = await statistic({
      ...formData,
    })
    if (res.success) {
      info.payout = res.data.payout
      info.totalBalance = res.data.totalBalance
    }
  } finally {
    uni.hideLoading()
  }
}
const goAllDetail = () => {
  uni.navigateTo({
    url: '/pages/paymentCenter/account-management/index'
  })


}

onMounted(() => {
  uni.showLoading({
    title: '加载中',
    mask: true
  })
  // 并行执行两个请求
  Promise.all([getList()])
    .finally(() => {
      uni.hideLoading()
    })
})
</script>

<style lang="scss" scoped>
.header-backgroundImage {
  width: 750rpx;
  background-size: cover;
  z-index: -1;
  position: absolute;

  :deep(.u-image) {
    div {
      border-bottom-left-radius: 40rpx !important;
      border-bottom-right-radius: 40rpx !important;
    }
  }
}

.my-title {
  height: 45rpx;
  font-size: 32rpx;
  font-family: $uni-font-family;
  font-weight: 400;
  color: #ffffff;
  line-height: 45rpx;
  margin-left: 23rpx;
  /* #ifdef MP-WEIXIN */
  padding-top: 80rpx;
  /* #endif */
  /* #ifdef APP-PLUS */
  padding-top: 60rpx;
  /* #endif */
  /* #ifdef H5 */
  padding-top: 7rpx;
  /* #endif */
}

.card-top {
  display: flex;
  padding-top: 30rpx;
}

.card-left {
  width: 132rpx;
  height: 132rpx;
  margin-left: 35rpx;
  flex-shrink: 0;
}

.card-image {
  width: 100%;
  height: 100%;
  background-size: cover;
}

.card-right {
  display: flex;
  flex-direction: column;
  margin-left: 30rpx;
  margin-top: 10rpx;
  flex: 1;
  color: #ffffff;
}

.card-time {
  width: 500rpx;
  // 超出宽度...
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 10rpx;
}

.borad {
  display: flex;
  justify-content: center;
}

.borad-box {
  margin-top: 30rpx;
  width: 90%;
  background: linear-gradient(to bottom, #fde9e5, #ffffff);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
  border-radius: 20rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-fenges {
    height: 160rpx;
    background: #eb958a;
    width: 5rpx;
  }

  .one-show {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .top-show-text {
      color: #333333;
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }

    .bottom-info {
      margin-top: 20rpx;

      .icon {
        font-size: 45rpx;
        color: #333333;
        font-weight: bold;
        padding-right: 5rpx;
      }
    }
  }
}

.title {
  margin-top: 30rpx;
  margin-left: 35rpx;
  color: #666666;
  font-size: 30rpx;
}

.pand-sort {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;

  .sort-box {
    width: 95%;
    background: linear-gradient(to bottom, #ffffff, #ffffff);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
    border-radius: 20rpx;

    .sort-title {
      padding: 20rpx 25rpx;
      font-size: 24rpx;
      display: flex;
      color: #666666;
      border-bottom: 1px solid #eee;

      .text-2 {
        padding-left: 10rpx;
        flex: 1;
        text-align: right;
      }
    }
  }
}

.accounts {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;

  .account-box {
    width: 90%;
    height: 80rpx;
    background: linear-gradient(to bottom, #ffffff, #ffffff);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;

    .icon-wxbzhanghu {
      color: #c20000;
      margin-right: 25rpx;
    }

    .icon-jinrujiantou {
      font-size: 40rpx;
    }

    padding: 0px 25rpx;
    box-sizing: border-box;

    .flex-1 {
      flex: 1;
    }
  }
}
</style>