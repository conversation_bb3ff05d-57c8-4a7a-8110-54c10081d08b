<template>
  <view class="page-box">
    <dingtalkNavBar title="账户明细" :hide-nav-bar="true"></dingtalkNavBar>
    <myTabs
      :modelData="modelData"
      :initIndex="initIndex"
      @change="tabChange"
    ></myTabs>
    <view class="content" v-if="dataList.length>0">
      <scroll-view
        scroll-y
        class="scrolls-view"
        refresher-enabled
        :refresher-triggered="refresherTriggered"
        style="height: calc(100vh - 200rpx)"
        lower-threshold="150"
        @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollLower"
      >
        <view class="box" v-for="(item,index) of dataList" :key="index">
          <view class="top">
            <view class="flow_number">流水编号：{{item.flowsCode}}</view>
            <view class="flex-1"></view>
            <view class="flow-status">{{$formatDictLabel(item.flowType,flow_type)}}</view>
          </view>
          <view class="middle">
            <view class="amount-info">
              <view class="amount-item">
                <text class="amount-label">消费前金额：</text>
                <text class="amount-value">￥{{ item.balance || '0.00' }}</text>
              </view>
              <view class="amount-item">
                <text class="amount-label">消费金额：</text>
                <text class="amount-value consumption">￥{{ item.spendingBalance || '0.00' }}</text>
              </view>
            </view>
          </view>
          <view class="bottom-info">
            <view class="amount-item">
              <text class="amount-label">消费后金额：</text>
              <text class="amount-value">￥{{ item.amount || '0.00' }}</text>
            </view>
          </view>
          <view class="bottom">
            <view class="date">支付时间：{{item.paymentTime}}</view>
            <view class="flex-1"></view>
            <view class="view-icon" @click="handleView(item)">
              <uni-icons type="eye" size="20" color="#c20000"></uni-icons>
            </view>
          </view>
        </view>

          <u-loadmore v-if="total > 0" :status="loadStatus" :load-text="{
            loadmore: '点击加载更多',
            loading: '正在加载...',
            nomore: '没有更多了',
          }" />
      </scroll-view>
    </view>
    <noData v-else style="padding-top: 50rpx;"></noData>
  </view>
</template>
  
  <script setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue'
import { detailsList } from '@/common/api/paymentCenter/account-management'
import { onPullDownRefresh, onLoad } from '@dcloudio/uni-app'
import myTabs from '@/components/myTabs/myTabs'
import { useUserStore } from '@/store/user.js'

const initIndex = ref(0)
const modelData = ref([{ label: '全部', value: '' }])
const userStore = useUserStore()
const dataList = ref([])

 const { proxy } = getCurrentInstance()
const { flow_type } = proxy.useDict('flow_type') 
// 响应式数据
const total = ref(0)
const loadStatus = ref('loadmore')
const formData = reactive({
  pageNum: 1,
  pageSize: 10,
  flowType: null,
  accountListId: '',
  staffId: ''
})

// 监听字典数据变化
watchEffect(() => {
  if (flow_type.value && flow_type.value.length > 0) {
    modelData.value = [
      { label: '全部', value: '' },
      ...(Array.isArray(flow_type.value) ? flow_type.value : []),
    ]

    if (modelData.value.length > 0) {
      formData.flowType = modelData.value[0]?.value ?? ''
    }
  }
})
// 在 script setup 中添加
const refresherTriggered = ref(false) // 下拉刷新状态

// 下拉刷新处理
const handleRefresh = async () => {
  if (refresherTriggered.value) return
  const startTime = Date.now()
  refresherTriggered.value = true

  try {
    formData.pageNum = 1
    await Promise.all([
      getList(),
      // 强制动画至少展示 500ms
      new Promise((resolve) => setTimeout(resolve, 500)),
    ])
  } finally {
    const elapsed = Date.now() - startTime
    const delay = elapsed < 500 ? 500 - elapsed : 0
    setTimeout(() => {
      refresherTriggered.value = false
    }, delay)
  }
}


// 加载更多
const handleScrollLower = () => {
  if (dataList.value.length >= total.value) return
  formData.pageNum++
  getList()
}

// 获取数据
const getList = async () => {
  try {
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)

    const res = await detailsList({
      ...formData,
    })

    if (res.success) {
      if (formData.pageNum === 1) {
        dataList.value = res.data.records
      } else {
        dataList.value = [...dataList.value, ...res.data.records]
      }
      total.value = res.data.total
      loadStatus.value =
        res.data.records.length < formData.pageSize ? 'nomore' : 'loadmore'
    }
  } finally {
    uni.hideLoading()
  }
}

const handleView = (item) => {
  uni.navigateTo({
    url: `/pages/paymentCenter/account-management/detail?item=${JSON.stringify(item)}`,
  })
}
const tabChange = (index) => {
  formData.flowType =  modelData.value[index].value
  formData.pageNum = 1
  initIndex.value = index
  getList()
}

// 获取页面跳转参数
onLoad((options) => {
  if (options.accountListId) {
    formData.accountListId = options.accountListId
  }
  if (options.staffId) {
    formData.staffId = options.staffId
  }
})

// 静态测试数据
const createStaticData = () => {
  dataList.value = [
    {
      flowsCode: 'FL202412280001',
      flowType: '1', // 对应字典中的消费
      balance: '1000.00',
      spendingBalance: '50.00',
      amount: '950.00',
      paymentTime: '2024-12-28 10:30:25',
      accountName: '餐饮账户'
    },
    {
      flowsCode: 'FL202412280002',
      flowType: '2', // 对应字典中的充值
      balance: '950.00',
      spendingBalance: '200.00',
      amount: '1150.00',
      paymentTime: '2024-12-28 14:15:10',
      accountName: '餐饮账户'
    },
    {
      flowsCode: 'FL202412280003',
      flowType: '1', // 对应字典中的消费
      balance: '1150.00',
      spendingBalance: '25.50',
      amount: '1124.50',
      paymentTime: '2024-12-28 18:45:33',
      accountName: '餐饮账户'
    },
    {
      flowsCode: 'FL202412270001',
      flowType: '1', // 对应字典中的消费
      balance: '1124.50',
      spendingBalance: '80.00',
      amount: '1044.50',
      paymentTime: '2024-12-27 12:20:15',
      accountName: '餐饮账户'
    },
    {
      flowsCode: 'FL202412270002',
      flowType: '3', // 对应字典中的退款
      balance: '1044.50',
      spendingBalance: '15.00',
      amount: '1059.50',
      paymentTime: '2024-12-27 16:30:42',
      accountName: '餐饮账户'
    }
  ]
  total.value = 5
  loadStatus.value = 'nomore'
}

onMounted(() => {
  uni.showLoading({
    title: '加载中',
  })
  // 使用静态数据进行测试
  createStaticData()
  uni.hideLoading()

  // 正式环境时使用真实接口
  // getList()
})
</script>
  
  <style lang="scss" scoped>
.page-box {
  background: #f5f5f5;
  height: calc(100vh - 88rpx);
}
.content {
  display: flex;
  justify-content: center;
  margin: 0px 30rpx;

  .box {
    width: 100%;
    margin-top: 24rpx;
    background: linear-gradient(to bottom, #ffffff, #ffffff);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
    border-radius: 20rpx;
    padding: 24rpx;
    padding-top: 0px;
    box-sizing: border-box;
    .top {
      height: 80rpx;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eee;
      font-size: 28rpx;
      .flow_number {
        color: #333333;
        display: inline-block;
        width: 80%;
        // 超出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .flow-status {
        background: #e2f7e8;
        color: #5cb073;
        padding: 5rpx 20rpx;
        font-size: 24rpx;
        white-space:nowrap;
      }
    }
    .middle {
      margin-top: 20rpx;
      .amount-info {
        display: flex;
        justify-content: space-between;
        .amount-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          .amount-label {
            font-size: 24rpx;
            color: #666666;
            margin-bottom: 8rpx;
          }
          .amount-value {
            font-size: 28rpx;
            color: #333333;
            font-weight: 500;
            &.consumption {
              color: #f56c6c;
            }
          }
        }
      }
    }
    .bottom-info {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      .amount-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .amount-label {
          font-size: 24rpx;
          color: #666666;
          margin-bottom: 8rpx;
        }
        .amount-value {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
        }
      }
    }
    .bottom {
      display: flex;
      align-items: center;
      margin-top: 20rpx;
      .date {
        color: #666666;
        font-size: 28rpx;
        display: inline-block;
        width: 80%;
      }
      .view-icon {
        padding: 10rpx;
        border-radius: 8rpx;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
        &:hover {
          background: #e0e0e0;
        }
      }
    }
  }
}
.flex-1 {
  flex: 1;
}
</style>
  