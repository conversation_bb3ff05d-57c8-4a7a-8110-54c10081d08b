<template>
  <view class="page-box">
    <dingtalkNavBar title="账户明细" :hide-nav-bar="true"></dingtalkNavBar>
    <myTabs
      :modelData="modelData"
      :initIndex="initIndex"
      @change="tabChange"
    ></myTabs>
    <view class="content" v-if="dataList.length>0">
      <scroll-view
        scroll-y
        class="scrolls-view"
        refresher-enabled
        :refresher-triggered="refresherTriggered"
        style="height: calc(100vh - 200rpx)"
        lower-threshold="150"
        @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollLower"
      >
        <view class="box" v-for="(item,index) of dataList" :key="index">
          <view class="top">
            <view class="flow_number">流水编号：{{item.flowsCode}}</view>
            <view class="flex-1"></view>
            <view class="flow-status">{{$formatDictLabel(item.flowType,flow_type)}}</view>
          </view>
          <view class="middle">
            <view class="card">{{item.accountName}}</view>
            <view class="flex-1"></view>
            <view class="date">￥{{ item.spendingBalance }}</view>
          </view>
          <view class="bottom">
            <view class="date">{{item.paymentTime}}</view>
            <view class="flex-1"></view>
            <view class="view-icon" @click="handleView(item)">
              <uni-icons type="eye" size="20" color="#c20000"></uni-icons>
            </view>
          </view>
        </view>

          <u-loadmore v-if="total > 0" :status="loadStatus" :load-text="{
            loadmore: '点击加载更多',
            loading: '正在加载...',
            nomore: '没有更多了',
          }" />
      </scroll-view>
    </view>
    <noData v-else style="padding-top: 50rpx;"></noData>
  </view>
</template>
  
  <script setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue'
import { detailsList } from '@/common/api/paymentCenter/account-management'
import { onPullDownRefresh, onLoad } from '@dcloudio/uni-app'
import myTabs from '@/components/myTabs/myTabs'
import { useUserStore } from '@/store/user.js'

const initIndex = ref(0)
const modelData = ref([{ label: '全部', value: '' }])
const userStore = useUserStore()
const dataList = ref([])

 const { proxy } = getCurrentInstance()
const { flow_type } = proxy.useDict('flow_type') 
// 响应式数据
const total = ref(0)
const loadStatus = ref('loadmore')
const formData = reactive({
  pageNum: 1,
  pageSize: 10,
  flowType: null,
  accountListId: '',
  staffId: ''
})

// 监听字典数据变化
watchEffect(() => {
  if (flow_type.value && flow_type.value.length > 0) {
    modelData.value = [
      { label: '全部', value: '' },
      ...(Array.isArray(flow_type.value) ? flow_type.value : []),
    ]

    if (modelData.value.length > 0) {
      formData.flowType = modelData.value[0]?.value ?? ''
    }
  }
})
// 在 script setup 中添加
const refresherTriggered = ref(false) // 下拉刷新状态

// 下拉刷新处理
const handleRefresh = async () => {
  if (refresherTriggered.value) return
  const startTime = Date.now()
  refresherTriggered.value = true

  try {
    formData.pageNum = 1
    await Promise.all([
      getList(),
      // 强制动画至少展示 500ms
      new Promise((resolve) => setTimeout(resolve, 500)),
    ])
  } finally {
    const elapsed = Date.now() - startTime
    const delay = elapsed < 500 ? 500 - elapsed : 0
    setTimeout(() => {
      refresherTriggered.value = false
    }, delay)
  }
}


// 加载更多
const handleScrollLower = () => {
  if (dataList.value.length >= total.value) return
  formData.pageNum++
  getList()
}

// 获取数据
const getList = async () => {
  try {
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)

    const res = await detailsList({
      ...formData,
    })

    if (res.success) {
      if (formData.pageNum === 1) {
        dataList.value = res.data.records
      } else {
        dataList.value = [...dataList.value, ...res.data.records]
      }
      total.value = res.data.total
      loadStatus.value =
        res.data.records.length < formData.pageSize ? 'nomore' : 'loadmore'
    }
  } finally {
    uni.hideLoading()
  }
}

const handleView = (item) => {
  uni.navigateTo({
    url: `/pages/paymentCenter/account-management/detail?item=${JSON.stringify(item)}`,
  })
}
const tabChange = (index) => {
  formData.flowType =  modelData.value[index].value
  formData.pageNum = 1
  initIndex.value = index
  getList()
}

// 获取页面跳转参数
onLoad((options) => {
  if (options.accountListId) {
    formData.accountListId = options.accountListId
  }
  if (options.staffId) {
    formData.staffId = options.staffId
  }
})

onMounted(() => {
  uni.showLoading({
    title: '加载中',
  })
  getList()
})
</script>
  
  <style lang="scss" scoped>
.page-box {
  background: #f5f5f5;
  height: calc(100vh - 88rpx);
}
.content {
  display: flex;
  justify-content: center;
  margin: 0px 30rpx;

  .box {
    width: 100%;
    margin-top: 24rpx;
    background: linear-gradient(to bottom, #ffffff, #ffffff);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 添加阴影效果
    border-radius: 20rpx;
    padding: 24rpx;
    padding-top: 0px;
    box-sizing: border-box;
    .top {
      height: 80rpx;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eee;
      font-size: 28rpx;
      .flow_number {
        color: #333333;
        display: inline-block;
        width: 80%;
        // 超出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .flow-status {
        background: #e2f7e8;
        color: #5cb073;
        padding: 5rpx 20rpx;
        font-size: 24rpx;
        white-space:nowrap;
      }
    }
    .middle {
      display: flex;
      align-items: center;
      margin-top: 20rpx;
      .card {
        background: #fff4e4;
        color: #f6a824;
        padding: 5rpx 20rpx;
         font-size: 28rpx;
      }
    }
    .bottom {
      display: flex;
      align-items: center;
      margin-top: 20rpx;
      .date {
        color: #666666;
        font-size: 28rpx;
        display: inline-block;
        width: 80%;
      }
      .view-icon {
        padding: 10rpx;
        border-radius: 8rpx;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
        &:hover {
          background: #e0e0e0;
        }
      }
    }
  }
}
.flex-1 {
  flex: 1;
}
</style>
  